import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Dynamic Residential Proxy - Dynamic IP as low as $2.4/GB - ElfProxy Pure IP Proxy Pool',
    ),
    description: t(locale)(
      'Global pool of over 100 million pure residential IPs, free trial of dynamic residential IP. Each IP is sourced from residential IP addresses of different devices worldwide, ensuring high IP purity, anonymous access, and no IP blocking. We also provide HTTP proxies, Socks5 proxies, and private residential IPs.',
    ),
    keywords: t(locale)(
      'Dynamic IP, Dynamic Proxy, Dynamic Residential IP, Dynamic Residential Proxy, Buy Dynamic IP, Try Dynamic IP, IP Proxy Pool',
    ),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com'}/proxies/dynamic-residential-proxy`,
    },
  };
};

function Proxy() {
  return <ProxyTemplate id="proxy-1" />;
}

export default Proxy;
