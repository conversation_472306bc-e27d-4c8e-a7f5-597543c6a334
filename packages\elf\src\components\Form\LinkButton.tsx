import type React from 'react';
import { type ReactNode } from 'react';

import ArrowIcon from '@hi7/assets/icon/arrow-right.svg';
import RightIcon from '@hi7/assets/icon/chevron-right.svg';
import clsx from 'clsx';
import Link from '../Link';

type HelperProps = {
  children: string | ReactNode;
  disabled?: boolean;
  type: 'main' | 'secondary' | 'text';
  size: 'L' | 'M' | 'S';
  url: string;
  target?: string;
  className?: string;
  onClick?: (e: any) => void;
};

const LinkButton: React.FC<HelperProps> = ({
  children,
  type,
  size,
  url,
  target,
  disabled,
  className,
  onClick,
}) => {
  return (
    <Link
      url={url}
      target={target}
      className={clsx(
        type !== 'text' && size === 'L' && 'px-6 py-3',
        type !== 'text' && size === 'M' && 'px-5 py-2',
        type !== 'text' && size === 'S' && 'px-3 py-1',
        size === 'L' && 'text-[16px]',
        size === 'M' && 'text-[14px]',
        size === 'S' && 'text-[12px]',
        'group flex cursor-pointer gap-2 rounded-3xl font-semibold whitespace-nowrap transition-all',
        type === 'main' &&
          'border border-[#FFC525] bg-[#FFC525] text-[#1E1E1E] hover:bg-transparent hover:text-[#FFC525] focus:bg-[#F90]',
        type === 'secondary' &&
          'border border-[#FFC525] bg-transparent text-[#FFC525] hover:bg-[#FFC525] hover:text-[#1E1E1E] focus:text-[#F90]',
        type === 'text' && 'bg-transparent text-[#FFC525] focus:text-[#F90]',
        className,
      )}
      onClick={onClick}
    >
      {children}
      <RightIcon
        className="flex group-hover:hidden"
        width={size === 'L' ? 24 : size === 'M' ? 20 : 16}
        height={size === 'L' ? 24 : size === 'M' ? 20 : 16}
      />
      <ArrowIcon
        className="hidden group-hover:flex"
        width={size === 'L' ? 24 : size === 'M' ? 20 : 16}
        height={size === 'L' ? 24 : size === 'M' ? 20 : 16}
      />
    </Link>
  );
};

export default LinkButton;
