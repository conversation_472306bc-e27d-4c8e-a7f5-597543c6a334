'use client';

import type { RefObject } from 'react';
import { useEffect } from 'react';
import { create } from 'zustand';

type ScrollElement = HTMLElement | null;

type ScrollOptions = {
  // Add threshold to determine when isScrolled becomes true
  threshold?: number;
  // Optional debounce delay in milliseconds
  debounceDelay?: number;
};

type ScrollState = {
  isScrolled: boolean;
  scrollTop: number;
  setScrollPosition: (isScrolled: boolean, scrollTop: number) => void;
};

// Create store
export const useScrollStore = create<ScrollState>((set) => ({
  isScrolled: false,
  scrollTop: 0,
  setScrollPosition: (isScrolled: boolean, scrollTop: number) =>
    set({ isScrolled, scrollTop }),
}));

export const useScrollContainer = (
  containerRef: RefObject<ScrollElement>,
  options: ScrollOptions = {},
) => {
  const { threshold = 0, debounceDelay = 0 } = options;
  const setScrollPosition = useScrollStore((state) => state.setScrollPosition);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let timeoutId: NodeJS.Timeout | null = null;

    const handleScroll = () => {
      const updateScrollData = () => {
        const scrollTop = container.scrollTop;
        setScrollPosition(scrollTop > threshold, scrollTop);
      };

      if (debounceDelay > 0) {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(updateScrollData, debounceDelay);
      } else {
        updateScrollData();
      }
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial check

    return () => {
      container.removeEventListener('scroll', handleScroll);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [containerRef, threshold, debounceDelay, setScrollPosition]);
};

// Hook to access scroll state anywhere
export const useScrollContainerData = () => {
  const isScrolled = useScrollStore((state) => state.isScrolled);
  const scrollTop = useScrollStore((state) => state.scrollTop);
  return { isScrolled, scrollTop };
};
