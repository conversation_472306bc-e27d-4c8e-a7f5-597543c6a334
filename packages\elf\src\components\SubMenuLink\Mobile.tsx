'use client';

import ChevronDown from '@hi7/assets/icon/chevron-down.svg';
import ChevronUp from '@hi7/assets/icon/chevron-up.svg';
import type { MenuLinkProps } from '@hi7/interface/link';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { useState } from 'react';
import Link from '../Link';

const SubMenuLinkMobile = ({
  children,
  onClick,

  items = [],
}: OmitStrict<MenuLinkProps, 'asButton'>) => {
  const hasSubitem = items.length > 0;

  const [isOpen, setIsOpen] = useState(false);
  const t = useI18n();

  return (
    <>
      <div
        onClick={() => {
          setIsOpen((prev) => !prev);
        }}
        className={'flex cursor-pointer items-center gap-2.5 py-4'}
      >
        {t(children as string)}
        {isOpen ? <ChevronUp /> : <ChevronDown />}
      </div>

      {hasSubitem && (
        <>
          {isOpen && (
            <div className="flex flex-col gap-y-4">
              {items.map(({ url, text, subText, Icon, order }) => (
                <Link
                  onClick={(e) => onClick?.(e)}
                  url={url}
                  key={`${text}-${url}`}
                  className={clsx(
                    'grid h-full grid-cols-[32px_1fr] items-center gap-x-2.5',
                    'cursor-pointer text-start text-black',
                    order,
                  )}
                >
                  {Icon && <Icon width={32} height={32} />}
                  <div className="text-sm">
                    <b className="font-semibold">{t(text)}</b>
                    <p className="text-[#1e1e1e]/65">{t(subText as string)}</p>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default SubMenuLinkMobile;
