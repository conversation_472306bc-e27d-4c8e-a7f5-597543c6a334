'use client';

import PriceBg from '@hi7/assets/background/price-bg.svg';
import Check from '@hi7/assets/icon/check.svg';
import HomeClient from '@hi7/assets/lotties/home-client';
import maskHomeClient from '@hi7/assets/lotties/home-client.png';
import LinkButton from '@hi7/components/Form/LinkButton';
import Lottie from '@hi7/components/Lottie';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import Image from 'next/image';
import { useState } from 'react';
import { FEATURES, PLANS } from './config';

function Pricing() {
  const [selected, setSelected] = useState(0);
  const { packages } = PLANS[selected];
  const t = useI18n();
  const locale = useLocale();

  return (
    <>
      <div className="flex items-center justify-center overflow-hidden">
        <div className="w-full">
          <div className="relative mb-[40px] min-h-(--min-sm-h-hvh) overflow-hidden rounded-br-[40px] rounded-bl-[40px] bg-[#002035] pb-[160px] lg:mb-0 lg:min-h-[100vh] lg:rounded-br-[100px] lg:rounded-bl-[100px] lg:pt-[78px] lg:pb-0">
            <div className="absolute bottom-[0px] left-[50%] translate-x-[-50%] lg:bottom-0">
              <PriceBg className="w-[380dvw] lg:w-screen" />
            </div>

            <div className="relative flex flex-col items-center justify-center px-5 py-11 lg:pt-[60px]">
              <div className="lg:w-[80dvw] lg:min-w-[855px]">
                <h1 className="mb-[34px] text-center text-[46px] leading-none text-white lg:text-[64px]">
                  {t('Choose Your Ideal Plan')}
                </h1>

                <div className="m-auto mb-[24px] flex flex-row justify-center gap-2.5 text-center lg:mb-[40px] lg:max-w-[80%]">
                  {PLANS.map((plan, index) => (
                    <div
                      key={`plan-${plan.name}`}
                      className={clsx(
                        'cursor-pointer rounded-[50px] border border-[#FFC525] px-3 py-2 text-[12px] leading-[1.4] font-semibold transition-all lg:w-[180px] lg:px-6 lg:py-4 lg:text-[16px]',
                        selected === index
                          ? 'bg-[#FFC525] text-[#1E1E1E]'
                          : 'bg-transparent text-[#FFC525]',
                      )}
                      onClick={() => setSelected(index)}
                    >
                      {t(plan.name)}
                    </div>
                  ))}
                </div>
                <div className="flex flex-col justify-center gap-5 lg:flex-row">
                  {packages.map((pkg, idx) => (
                    <div
                      key={`pkg1-${idx}`}
                      className="flex flex-col gap-4 rounded-[20px] bg-white px-[20px] py-[30px] text-[#1E1E1E] lg:w-[360px]"
                    >
                      <div className="hidden lg:block">
                        <pkg.Icon width={48} />
                      </div>
                      <b className="text-[24px] leading-[1.5] font-semibold text-[#007D93]">
                        {t(pkg.name)}
                      </b>
                      <div className="flex lg:block">
                        <div className="flex-1">
                          <p>{t('as low as')}</p>
                          <b className="text-[32px] leading-[1.2] font-semibold">
                            ${pkg.price.amount}
                            <span className="text-[18px] leading-[1.4] font-normal">
                              /{t(pkg.price.unit)}
                            </span>
                          </b>
                        </div>
                        <LinkButton
                          url={
                            locale === 'en'
                              ? 'https://007tg.com/ccs/elfproxy'
                              : 'https://007tg.com/ccs/elfproxy'
                          }
                          target="_blank"
                          type="main"
                          size="L"
                          className="flex self-end lg:hidden"
                        >
                          {t('Buy Now')}
                        </LinkButton>
                      </div>
                      <LinkButton
                        url={
                          locale === 'en'
                            ? 'https://007tg.com/ccs/elfproxy'
                            : 'https://007tg.com/ccs/elfproxy'
                        }
                        target="_blank"
                        type="main"
                        size="L"
                        className="mb-2 hidden justify-center lg:flex"
                      >
                        {t('Buy Now')}
                      </LinkButton>

                      <ul className="flex flex-col gap-4">
                        {pkg.features.map((feature, fIdx) => (
                          <li
                            key={`pkg-${fIdx}`}
                            className="grid grid-cols-[18px_1fr] items-start gap-2.5 text-[16px] leading-[1.2]"
                          >
                            <Check className="w-[18px] text-[#27CA40]" />
                            {t(feature)}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>

                <div className="flex items-center justify-center pt-[40px] lg:pt-[84px]">
                  <LinkButton
                    type="text"
                    size="L"
                    url={
                      locale === 'en'
                        ? 'https://007tg.com/ccs/elfproxy'
                        : 'https://007tg.com/ccs/elfproxy'
                    }
                    target="_blank"
                  >
                    {t(
                      'Technical engineers are available 24/7 to recommend the best proxy IP for you.',
                    )}
                  </LinkButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex overflow-hidden">
        <div className="w-full">
          <div className="relative flex flex-col items-center gap-[40px] px-5 lg:px-30 lg:py-[100px]">
            <h2 className="text-[32px] lg:text-[48px]">
              {t("Why Choose ElfProxy's?")}
            </h2>
            <div className="grid grid-cols-1 gap-[40px] pb-[24px] lg:grid-cols-4 lg:gap-[28px]">
              {FEATURES.map((feat, idx) => (
                <div key={`feat-${idx}`} className="flex flex-col gap-4">
                  <feat.Icon className="h-[72px] w-[72px]" />
                  <b className="text-[24px] leading-[1.2] font-semibold text-[#002035]">
                    {t(feat.title)}
                  </b>
                  <p>{t(feat.desc)}</p>
                </div>
              ))}
            </div>
          </div>
          <div className="relative h-[120px] w-screen overflow-hidden lg:bottom-0 lg:h-[113px]">
            <div className="animate-home-client-cat absolute right-0 bottom-[-40px] h-[150px] w-[150px]">
              <Lottie
                animationData={HomeClient}
                masking={
                  <Image
                    src={maskHomeClient}
                    alt={''}
                    height={150}
                    width={150}
                  />
                }
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Pricing;
