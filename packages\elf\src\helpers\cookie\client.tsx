'use client';

import COOKIES from '@hi7/configs/cookies';
import type { ResponseCookie } from 'next/dist/compiled/@edge-runtime/cookies';

export function getCookies(
  name: string,
  prefix: string | boolean = COOKIES.common,
) {
  if (typeof document === 'undefined') {
    return null;
  }

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${prefix ? `${prefix}-${name}` : name}=`);

  if (parts.length === 2) {
    return parts.pop()!.split(';').shift();
  }
  return null;
}

export function setCookies(
  name: string,
  value: string,
  options?: Partial<ResponseCookie>,
) {
  if (typeof document === 'undefined') {
    return;
  }

  let cookie = `${name}=${value}; path=/;`;

  if (options?.domain) {
    cookie += ` domain=${options.domain};`;
  }

  if (options?.maxAge) {
    cookie += ` max-age=${options.maxAge};`;
  }

  document.cookie = cookie;
}
