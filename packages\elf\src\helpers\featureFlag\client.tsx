import type { FeatureFlagId } from '@hi7/interface/feature-flag';
import useFeatureFlagStore from '@hi7/lib/stores/featureFlagStore';

export const useFeatureFlag = (flagId: FeatureFlagId) => {
  const ffStore = useFeatureFlagStore((state) => state.featureFlag);
  return ffStore[flagId];
};

export const useIsFullPageScrollEnabled = () => {
  useFeatureFlag('feat-80247500');
  return;
};

export const useRemoveProductsEnabled = () => {
  useFeatureFlag('feat-80264622');
  return;
};
