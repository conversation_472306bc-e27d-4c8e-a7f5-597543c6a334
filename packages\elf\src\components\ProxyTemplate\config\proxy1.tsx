import P1 from '@hi7/assets/icon/proxy-1-1.svg';
import P2 from '@hi7/assets/icon/proxy-1-2.svg';
import P3 from '@hi7/assets/icon/proxy-1-3.svg';
import P4 from '@hi7/assets/icon/proxy-1-4.svg';
import P5 from '@hi7/assets/icon/proxy-1-5.svg';
import P6 from '@hi7/assets/icon/proxy-1-6.svg';
import proxy2 from '@hi7/assets/lotties/proxy-1';

export const benefit = [
  {
    Icon: P1,
    title: 'Authentic Residential IPs',
    desc: 'Experience true residential connections that effortlessly bypass geo-blocks.',
  },
  {
    Icon: P2,
    title: 'Vast IP Resources',
    desc: 'Tap into a pool of billions of IPs with minimal duplication.',
  },
  {
    Icon: P3,
    title: 'High Performance',
    desc: 'Enjoy unlimited speed with a flexible, on-demand billing model.',
  },
] as const;

export const useCase = [
  {
    Icon: P4,
    title: 'Market Research',
    desc: 'Quickly gather the latest market data for strategic insights.',
  },
  {
    Icon: P5,
    title: 'Social Media Management',
    desc: 'Management: Efficiently manage multiple accounts using localised IPs.',
  },
  {
    Icon: P6,
    title: 'Data Scraping',
    desc: 'Reliably collect data while reducing the risk of detection or bans.',
  },
] as const;

export const PROXY = {
  benefit,
  useCase,
  cta: 'Starting at $2.4/GB',
  theme: 'dark',
  title: {
    h3: 'The Most Affordable',
    h1: 'Dynamic Residential Proxy',
    p: 'With ElfProxy’s Dynamic Residential Proxy, effortlessly bypass regional restrictions and access data from anywhere. Our dynamic residential IPs are carefully selected to ensure high quality and anonymity-providing you with a competitive edge in data collection and online operations.',
  },
  background: {
    left: 'bg-[#007D93]',
    right: 'text-[#004A61]',
  },
  lottie: {
    // Masking: MaskProxy1, // TBC
    data: proxy2,
  },
} as const;

export default PROXY;
