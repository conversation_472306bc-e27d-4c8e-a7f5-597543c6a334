import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Best SEO Monitoring Proxy - SERP Data Monitoring - Avoid IP Blocking Risks.',
    ),
    description: t(locale)(
      'ElfProxy offers SEO monitoring proxy IP services, leveraging a global network of dynamic residential IPs to real-time track keyword rankings, search trends, and competitor data, providing precise insights into industry trends and competitor activities. Enjoy unrestricted access to Google and other search engines while avoiding the risk of IP blocking. Start your free trial now!',
    ),
    keywords: t(locale)(
      'SEO Monitoring Optimization, Search Engine Data Monitoring, Search Engine Monitoring, Website Ranking Optimization, SEO Keyword Optimization, SEO Analysis, Dynamic IP',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-10" />;
}

export default Solution;
