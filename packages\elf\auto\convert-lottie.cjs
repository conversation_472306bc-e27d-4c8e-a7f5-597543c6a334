const fs = require('fs');
const path = require('path');

// Directories
const lottiesDir = path.join(__dirname, '../src/assets/lotties');
const outputPartsDir = path.join(lottiesDir, 'parts');

// Ensure output directory exists
if (!fs.existsSync(outputPartsDir)) {
  fs.mkdirSync(outputPartsDir, { recursive: true });
}

// Helper to extract base64 images from lottie JSON
function extractBase64Images(obj, images = []) {
  if (Array.isArray(obj)) {
    obj.forEach((item) => extractBase64Images(item, images));
  } else if (typeof obj === 'object' && obj !== null) {
    if (obj.p && typeof obj.p === 'string' && obj.p.startsWith('data:image/')) {
      images.push(obj);
    }
    Object.values(obj).forEach((val) => extractBase64Images(val, images));
  }
  return images;
}

// Main process
fs.readdirSync(lottiesDir).forEach((file) => {
  if (!file.endsWith('.json')) return;
  const filePath = path.join(lottiesDir, file);
  const lottie = JSON.parse(fs.readFileSync(filePath, 'utf8'));

  // Find all base64 images
  const images = extractBase64Images(lottie);

  // Collect import statements and mapping
  const importStatements = [];
  const imgVarNames = [];

  images.forEach((imgObj, idx) => {
    const match = imgObj.p.match(/^data:(image\/\w+);base64,(.+)$/);
    if (!match) return;
    const ext = match[1].split('/')[1];
    const base64Data = match[2];
    const outFileName = `${path.basename(file, '.json')}_${idx}.${ext}`;
    const outFilePath = path.join(outputPartsDir, outFileName);

    // Save image file
    fs.writeFileSync(outFilePath, Buffer.from(base64Data, 'base64'));

    // Prepare import variable name
    const varName = `${path.basename(file, '.json')}_${idx}`.replace(/-/g, '_');
    importStatements.push(`import ${varName} from './parts/${outFileName}';`);
    imgVarNames.push(varName);

    // Replace base64 with import variable
    imgObj.p = varName + '.src';
  });

  // Save modified lottie as .ts file with imports
  if (images.length > 0) {
    const tsFileName = file.replace(/\.json$/, '.ts');
    const tsFilePath = path.join(lottiesDir, tsFileName);
    // Convert lottie object to string, replacing import variable references
    let lottieStr = JSON.stringify(lottie, null, 2);
    // Replace quoted import variable references with unquoted ones (for .src)
    lottieStr = lottieStr.replace(
      /"([a-zA-Z0-9_]+)\.src"/g,
      (match, varName) => `${varName}.src`,
    );

    const tsContent =
      importStatements.join('\n') + '\n\n' + `export default ${lottieStr};\n`;

    fs.writeFileSync(tsFilePath, tsContent);
    fs.unlinkSync(filePath); // Optionally remove original .json
  }
});

console.log(
  'Lottie base64 images extracted, saved to lotties/parts, and .ts files generated with imports.',
);
