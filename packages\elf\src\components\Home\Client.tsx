'use client';

import HomeClient from '@hi7/assets/lotties/home-client';
import maskHomeClient from '@hi7/assets/lotties/home-client.png';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import Image from 'next/image';
import { UIEventHandler, useState } from 'react';
import Lottie from '../Lottie';
import { FEEDBACK_DATA } from './config';

function Client() {
  const [index, setIndex] = useState(0);

  const t = useI18n();
  const handleScroll: UIEventHandler = (e) => {
    const cards = e.currentTarget.firstElementChild?.children || [];

    let foundIndex = 0;
    for (let i = 0; i < cards.length; i++) {
      const rect = (cards[i] as HTMLElement).getBoundingClientRect();

      const containerRect = e.currentTarget.getBoundingClientRect();
      const thresold = 300;
      if (
        rect.right - thresold > containerRect.left &&
        rect.left - thresold < containerRect.right
      ) {
        foundIndex = i;
        break;
      }
    }
    setIndex(foundIndex);
  };

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative">
          <div className="relative flex flex-col items-center justify-center px-5 py-11 text-[#002035] lg:pt-[72px]">
            <div className="w-full lg:w-[85dvw] lg:min-w-[1200px]">
              <h2 className="mb-[70px] text-center text-[36px] leading-none lg:text-[48px]">
                {t('What ')}
                <span className="font-orbitron font-semibold text-[#31F4A0]">
                  {t('Our Clients')}
                </span>
                {t(' Are Saying')}
              </h2>

              <div
                className="no-scrollbar w-full overflow-x-auto pt-[60px] pb-[80px] lg:w-[85dvw] lg:pb-[160px]"
                onScroll={handleScroll}
                style={{ cursor: 'grab' }}
                onMouseDown={(e) => {
                  const container = e.currentTarget as HTMLDivElement;
                  let startX = e.pageX - container.offsetLeft;
                  let scrollLeft = container.scrollLeft;
                  let isDown = true;

                  const onMouseMove = (moveEvent: MouseEvent) => {
                    if (!isDown) return;
                    moveEvent.preventDefault();
                    const x = moveEvent.pageX - container.offsetLeft;
                    const walk = x - startX;
                    container.scrollLeft = scrollLeft - walk;
                  };

                  const onMouseUp = () => {
                    isDown = false;
                    window.removeEventListener('mousemove', onMouseMove);
                    window.removeEventListener('mouseup', onMouseUp);
                  };

                  window.addEventListener('mousemove', onMouseMove);
                  window.addEventListener('mouseup', onMouseUp);
                }}
              >
                <div
                  className="grid gap-x-[30px] px-[30px]"
                  style={{
                    width: `${FEEDBACK_DATA.length * 330}px`,
                    gridTemplateColumns: `repeat(${FEEDBACK_DATA.length}, minmax(330px, 1fr))`,
                  }}
                >
                  {FEEDBACK_DATA.map((item, idx) => (
                    <div
                      key={`fb-${idx}`}
                      className="relative mb-[30px] flex w-full shrink-0 flex-col items-center gap-[16px] rounded-[20px] p-6 px-[30px] pt-[80px] pb-[30px] text-[#1E1E1E] shadow-[0px_10px_30px_0px_rgba(0,0,114,0.15)]"
                    >
                      <Image
                        className="absolute top-[-60px]"
                        src={item.image}
                        alt={item.name}
                        width={100}
                        height={135}
                      />

                      <b>{t(item.name)}</b>
                      <p>{t(item.comments)}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="relative m-auto grid w-[168px] grid-cols-8 gap-x-3 pb-[120px] lg:hidden">
                {FEEDBACK_DATA.map((_, idx) => (
                  <div
                    key={`fbb-${idx}`}
                    className={clsx([
                      'h-[10px] w-[10px] cursor-pointer rounded-full transition-all duration-300 ease-in-out',
                      index === idx ? 'bg-[#31F4A0]' : 'bg-[#D9D9D9]',
                    ])}
                    onMouseEnter={() => setIndex(idx)}
                  ></div>
                ))}
              </div>

              <div className="animate-home-client-cat absolute right-0 bottom-[-40px] h-[150px] w-[150px]">
                <Lottie
                  animationData={HomeClient}
                  masking={
                    <Image
                      src={maskHomeClient}
                      alt={''}
                      height={150}
                      width={150}
                    />
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Client;
