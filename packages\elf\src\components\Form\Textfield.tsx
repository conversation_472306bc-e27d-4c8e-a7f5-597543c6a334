import Helper from '@hi7/components/Form/Helper';
import Label from '@hi7/components/Form/Label';
import { DEFAULT_INPUT_TYPE } from '@hi7/configs/input';
import { getInputClasses } from '@hi7/helpers/class';
import {
  getHelperOrder,
  getInputOrder,
  getLabelOrder,
} from '@hi7/helpers/input';
import type { InputProps } from '@hi7/interface/input';
import type React from 'react';
import type { ChangeEvent } from 'react';
import { useState } from 'react';
import { useController, useFormContext } from 'react-hook-form';

type TextFieldProps = InputProps;

const TextField: React.FC<TextFieldProps> = ({
  label,
  name,
  type = 'text',
  placeholder = '',
  required,
  variant = DEFAULT_INPUT_TYPE,
}) => {
  const { control, trigger } = useFormContext();
  const { field } = useController({ name, control });
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    trigger(name);
    field.onBlur();
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    field.onChange(e);
    trigger(name);
  };

  return (
    <div className="relative mb-6 flex flex-col">
      <input
        {...field}
        id={name}
        type={type}
        placeholder={placeholder}
        onBlur={handleBlur}
        onChange={handleChange}
        className={`${getInputClasses({ variant, value: field.value })} ${getInputOrder()}`}
        onFocus={handleFocus}
      />
      <Label
        name={name}
        required={required}
        isFocused={isFocused}
        variant={variant}
        value={field.value}
        classes={getLabelOrder()}
      >
        {label}
      </Label>
      <Helper name={name} classes={getHelperOrder()} />
    </div>
  );
};

export default TextField;
