import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import COOKIES from './configs/cookies';
import { DEFAULT_LOCALE } from './configs/dictionary';
import { SUPPORTED_LOCALES } from './configs/pathname';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './helpers/cookie';

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  console.log('Middleware processing path:', pathname);

  // Get locale from cookie or use default
  const cookieLocale = request.cookies.get(COOKIES.localization)?.value;
  const locale = SUPPORTED_LOCALES.includes(cookieLocale as string)
    ? cookieLocale
    : DEFAULT_LOCALE;

  // Set locale in response headers for server components to access
  const response = NextResponse.next();
  response.headers.set('x-locale', locale as string);
  response.headers.set('x-next-url', pathname);

  await initVisitorId(request, response);

  return response;
}

// cannot move to helper, as imported at middleware
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

const initVisitorId = async (request: NextRequest, response: NextResponse) => {
  const cookieHandler = new CookieHandler(request, response);
  const visitorId = cookieHandler.getCookie(COOKIES.visitor)?.value;

  if (visitorId) {
    response.headers.set('x-visitor-id', visitorId);
    return;
  }

  const newVisitorId = generateUUID();
  response.headers.set('x-visitor-id', newVisitorId);
  cookieHandler.setCookie({ name: COOKIES.visitor, value: newVisitorId });
};
export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|icon.svg|health-check|feature-flag|videos).*)',
  ],
};
