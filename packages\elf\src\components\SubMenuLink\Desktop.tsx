'use client';

import ChevronDown from '@hi7/assets/icon/chevron-down.svg';
import type { MenuLinkProps } from '@hi7/interface/link';
import { useI18n } from '@hi7/lib/i18n';
import { useHeader } from '@hi7/provider/HeaderProvider';
import clsx from 'clsx';
import Link from '../Link';

const SubMenuLink = ({
  url,
  children,

  items = [],
}: OmitStrict<MenuLinkProps, 'asButton'>) => {
  const hasSubitem = items.length > 0;

  const { activeMenu, openMenu, closeMenu } = useHeader();
  const t = useI18n();

  return (
    <div
      onClick={() => openMenu(url)}
      className={clsx('relative flex cursor-pointer items-center gap-2.5')}
    >
      {t(children as string)}

      {hasSubitem && (
        <>
          <ChevronDown />

          {activeMenu === url && (
            <div
              className={clsx(
                'absolute top-full z-50 mt-7 grid h-fit w-fit translate-x-[-25%] gap-x-10 gap-y-[30px] rounded-[10px] bg-white p-10',
                'shadow-[0_10px_15px_-3px_rgba(0,0,0,0.1),0_4px_6px_-2px_rgba(0,0,0,0.05),0_-1px_2px_rgba(0,0,0,0.03),-10px_0_20px_-5px_rgba(0,0,0,0.08),10px_0_20px_-5px_rgba(0,0,0,0.08)]',
                items.length <= 2 ? 'grid-cols-[1fr]' : 'grid-cols-[1fr_1fr]',
              )}
              onClick={() => {
                openMenu(url);
              }}
            >
              {items.map(({ url, text, subText, Icon, order }) => (
                <Link
                  url={url}
                  key={`${text}-${url}`}
                  className={clsx(
                    'grid h-full grid-cols-[40px_1fr] items-center gap-2.5 whitespace-nowrap',
                    'cursor-pointer text-start text-black',
                    order,
                  )}
                  onClick={() => closeMenu()}
                >
                  {Icon && <Icon width={40} height={40} />}
                  <div>
                    <b>{t(text)}</b>
                    <p>{t(subText as string)}</p>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default SubMenuLink;
