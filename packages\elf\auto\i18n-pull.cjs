const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

require('dotenv').config();

const FILE_NAME = 'Elfproxy Translation';
const DEFAULT_BRANCH = 'master';

async function getGoogleClient() {
  const credentials = {
    type: 'service_account',
    project_id: process.env.GOOGLE_PROJECT_ID,
    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,
    private_key: process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    client_email: process.env.GOOGLE_CLIENT_EMAIL,
    client_id: process.env.GOOGLE_CLIENT_ID,
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,
    universe_domain: 'googleapis.com',
  };

  const auth = new google.auth.GoogleAuth({
    credentials,
    scopes: ['https://www.googleapis.com/auth/drive.file'],
  });

  const clientAuth = await auth.getClient();

  const drive = google.drive({ version: 'v3', auth: clientAuth });
  const sheets = google.sheets({ version: 'v4', auth: clientAuth });

  return { drive, sheets };
}

async function getBranchname() {
  try {
    const name = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
    // Google Sheets tab names cannot contain: [ ] : * ? / \ and must be <= 100 chars
    const safeName = name.replace(/[\[\]:*?/\\]/g, '_').slice(0, 100);
    return safeName;
  } catch (err) {
    return `chores/sys-${new Date().toISOString()}`;
  }
}

async function syncFromGoogleSheet() {
  if (
    !process.env.GOOGLE_PROJECT_ID ||
    !process.env.GOOGLE_PRIVATE_KEY_ID ||
    !process.env.GOOGLE_PRIVATE_KEY ||
    !process.env.GOOGLE_CLIENT_EMAIL ||
    !process.env.GOOGLE_CLIENT_ID
  ) {
    console.error(
      '[ERROR] Missing required environment variables for Google API.\n' +
        'Please check your .env file and make sure all Google credentials are set.\n' +
        'Required: GOOGLE_PROJECT_ID, GOOGLE_PRIVATE_KEY_ID, GOOGLE_PRIVATE_KEY, GOOGLE_CLIENT_EMAIL, GOOGLE_CLIENT_ID',
    );
    process.exit(1);
  }

  const filePath = path.resolve(__dirname, '../src/dictionaries/zh.json');
  const { sheets, drive } = await getGoogleClient();

  const res = await drive.files.list({
    q: `name='${FILE_NAME}' and trashed=false`,
    fields: 'files(id, name)',
    spaces: 'drive',
  });

  if (!res.data.files || res.data.files.length === 0) {
    console.error('[ERROR] No Google Sheet found for translations.');
    process.exit(1);
  }

  const spreadsheetId = res.data.files[0].id;
  let branch = await getBranchname();

  const sheetMeta = await sheets.spreadsheets.get({ spreadsheetId });
  const sheet = sheetMeta.data.sheets.find(
    (s) => s.properties.title === branch,
  );
  if (!sheet) {
    console.warn(
      `[WARN] No tab found for branch "${branch}" in Google Sheet. Using default sheet "${DEFAULT_BRANCH}".`,
    );
    branch = DEFAULT_BRANCH;
  }

  // Read values from the sheet
  const range = `'${branch}'!A1:B1000`;
  const sheetData = await sheets.spreadsheets.values.get({
    spreadsheetId,
    range,
    majorDimension: 'ROWS',
  });

  const rows = sheetData.data.values || [];
  // Remove meta rows (first 2 rows)
  const dataRows = rows.slice(2);

  const malaysiaTime = new Date().toLocaleString('en-US', {
    timeZone: 'Asia/Kuala_Lumpur',
  });
  const sheetUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit#gid=${sheet ? sheet.properties.sheetId : 0}`;
  const zhData = {
    __SKIP_en_last_sync: malaysiaTime,
    __SKIP_en_sheet_url: sheetUrl,
  };
  dataRows.forEach(([key, value = '']) => {
    if (!key) return;
    const realKey = key.replace(/<end_space>$/, ' ');
    const realValue = value.replace(/<end_space>$/, ' ');

    zhData[realKey] = realValue;
  });

  const json = JSON.stringify(zhData, null, 2).replace(
    /(\n)(\s*"__SKIP_en_page_)/g,
    '\n\n$2',
  );

  fs.writeFileSync(filePath, json, 'utf-8');
  console.log(
    `[SUCCESS] Synced translations from Google Sheets (${branch}) to local zh.json`,
  );
}

syncFromGoogleSheet();
