import {
  HTML_CLASS_POST_DETAILS,
  HTML_CLASS_TERMS_PRIVACY,
} from '@hi7/configs/theme';
import clsx from 'clsx';
import parse, { Element } from 'html-react-parser';

export function replaceTermPrivacy(htmlContent: string) {
  return parse(htmlContent, {
    replace(domNode) {
      if (!(domNode instanceof Element)) {
        return;
      }

      const tagName = domNode.tagName as keyof typeof HTML_CLASS_TERMS_PRIVACY;
      const className = HTML_CLASS_TERMS_PRIVACY[tagName];

      if (!className) return;

      domNode.attribs.class = className;
    },
  });
}

export function displayOnlyFigures(
  htmlContent: string,
  height?: string,
  removeAutoplay = false,
): JSX.Element {
  // Match first figure tag with its content
  const firstFigureMatch = htmlContent.match(/<figure[^>]*>.*?<\/figure>/);

  if (!firstFigureMatch) {
    return <div />; // Return empty div if no figures found
  }

  let figure = firstFigureMatch[0].replace(
    /<img\s+([^>]*)>/g,
    (match, attributes) => {
      const classMatch = attributes.match(/class="([^"]*)"/);
      const existingClass = classMatch ? classMatch[1] : '';
      const cleanAttributes = attributes.replace(/\s*class="[^"]*"/, '');
      const mergedClass = clsx(existingClass, 'object-cover', height).trim();

      return `<img ${cleanAttributes} class="${mergedClass}">`;
    },
  );

  if (removeAutoplay) {
    figure = figure.replace(/ autoplay/, '');
  }

  return (
    <div
      dangerouslySetInnerHTML={{
        __html: figure,
      }}
    />
  );
}

export function processHtmlContent(htmlContent: string): JSX.Element {
  // First, remove only the first figure using regex
  const contentWithoutFirstFigure = htmlContent.replace(
    /<figure[^>]*>.*?<\/figure>/, // Removed the 'g' flag to match only the first occurrence
    '',
  );

  // Then apply the class replacements and set id for <h2> elements
  return parse(contentWithoutFirstFigure, {
    replace(domNode) {
      if (!(domNode instanceof Element)) {
        return;
      }

      const tagName = domNode.tagName as keyof typeof HTML_CLASS_POST_DETAILS;
      const className = HTML_CLASS_POST_DETAILS[tagName];

      if (className) {
        domNode.attribs.class = className;
      }

      // If the element is an <h2>, set an id based on its title
      if (tagName === 'h2') {
        const headingText = domNode.children
          .map((child) => (child.type === 'text' ? child.data : ''))
          .join('')
          .trim();

        // Generate an id by converting the heading text to a URL-friendly format
        const headingId = generateIdByText(headingText);

        domNode.attribs.id = headingId;
      }

      return domNode;
    },
  }) as JSX.Element;
}

export function generateIdByText(input: string) {
  return input
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]+/g, '')
    .replace(/-+/g, '-');
}
