'use client';

// import ContactButton from '@hi7/assets/icon/fab-contact-button.svg';
// import ContactClose from '@hi7/assets/icon/fab-contact-close.svg';
// import ContactZhText from '@hi7/assets/icon/fab-contact-text-zh.svg';
// import ContactText from '@hi7/assets/icon/fab-contact-text.svg';
// import ContactTG from '@hi7/assets/icon/fab-contact-tg.svg';
// import ContactWS from '@hi7/assets/icon/fab-contact-ws.svg';
import LatestNews from '@hi7/assets/icon/fab-latest-news.svg';
// import { useGaConversion } from '@hi7/helpers/google-ads';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { useRef, useState } from 'react';
import Link from '../Link';

function Fab() {
  // const invokeTGConversion = useGaConversion({
  //   conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_TG_CONVERSION_LABEL,
  // });
  // const invokeWAConversion = useGaConversion({
  //   conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_WA_CONVERSION_LABEL,
  // });

  const t = useI18n();
  const locale = useLocale();
  const [isLatestOpen, setIsLatestOpen] = useState(false);
  const clearLatestTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // const [isContactOpen, setIsContactOpen] = useState(false);

  function handleLatestMouseEnter() {
    clearLatestTimeoutRef.current &&
      clearTimeout(clearLatestTimeoutRef.current);
    setIsLatestOpen(true);
  }

  function handleLatestMouseLeave() {
    clearLatestTimeoutRef.current &&
      clearTimeout(clearLatestTimeoutRef.current);
    clearLatestTimeoutRef.current = setTimeout(() => {
      setIsLatestOpen(false);
    }, 500);
  }

  return (
    <>
      <Link
        url="https://blog.elfproxy.com"
        className={clsx(
          'fixed bottom-[23%] left-[20px] z-50 h-[48px] overflow-hidden rounded-full lg:left-[65px]',
          isLatestOpen ? 'w-[165px]' : 'w-[48px]',
        )}
        onMouseEnter={handleLatestMouseEnter}
        onMouseLeave={handleLatestMouseLeave}
      >
        {isLatestOpen && (
          <div
            className={clsx(
              'animate-fab-latest-news absolute top-[1px] left-0 h-[48px] rounded-[50px] bg-[#31F4A0] pr-4 text-right text-[14px] leading-[48px] font-bold text-[#002035]',
              locale === 'zh' ? 'w-[130px]' : 'w-[165px]',
            )}
          >
            {t('Latest News')}
          </div>
        )}
        <div className="relative flex h-[48px] w-[48px] items-center justify-center rounded-full bg-[#007D93]">
          <LatestNews />
        </div>
      </Link>

      {/* <div className="fixed right-0 bottom-[23%] z-50 lg:right-[65px]">
        <div
          className={clsx(
            locale === 'zh' ? 'translate-x-[-6px]' : 'translate-x-[-16px]',
            isContactOpen && 'animate-fab-contact-1',
          )}
        >
          {locale === 'zh' ? <ContactZhText /> : <ContactText />}
        </div>

        {isContactOpen && (
          <>
            <a
              className="animate-fab-contact-2 t z-1 block h-[48px] w-[48px]"
              href="https://007tg.com/ccs/elfproxy"
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => invokeTGConversion()}
            >
              <ContactTG />
            </a>

            <a
              className="animate-fab-contact-3 z-1 block h-[48px] w-[48px]"
              href="https://007tg.com/ccs/elfproxy_wa"
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => invokeWAConversion()}
            >
              <ContactWS />
            </a>
          </>
        )}

        <div
          className="relative z-2 flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full bg-[#FFC525]"
          onClick={() => setIsContactOpen((prev) => !prev)}
        >
          {isContactOpen ? <ContactClose /> : <ContactButton />}
        </div>
      </div> */}
    </>
  );
}

export default Fab;
