import type { MenuLinkProps } from '@hi7/interface/link';
import { ROUTES } from '../Header/config';

export const QUICK_ACCESS: MenuLinkProps[] = [
  { url: '/pricing', children: 'Pricing' },
  { url: '/resource/faq', children: 'FAQs' },
  { url: 'https://api.elfproxy.com/doc.html#/home', children: 'API' },
  { url: 'https://blog.elfproxy.com', children: 'Industry News' },
];

export const PROXIES: MenuLinkProps[] = (
  ROUTES.find((s) => s.url === '/proxies')?.items || []
).map((item) => ({
  url: item.url,
  children: item.text,
  order: item.order
    ?.split(' ')
    .filter((s) => !s.startsWith('lg:'))
    .join(' '),
}));

export const SOLUTIONS: MenuLinkProps[] = (
  ROUTES.find((s) => s.url === '/solutions')?.items || []
).map((item) => ({
  url: item.url,
  children: item.text,
  order: item.order
    ?.split(' ')
    .filter((s) => !s.startsWith('lg:'))
    .join(' '),
}));
