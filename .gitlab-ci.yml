stages:
  - build-image-hiseven
  - tf-validate-hiseven
  - tf-plan-hiseven
  - tf-apply-hiseven
  - rolling-update-hiseven

default:
  tags:
    - ali-runner

  image:
    name: hashicorp/terraform:1.5.7
    entrypoint:
      - '/usr/bin/env'
      - 'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'

build-image-hiseven:
  stage: build-image-hiseven
  needs: []
  image: docker:26.1.4-dind
  variables:
    DOCKER_TLS_CERTDIR: /certs
  script:
    - echo "Entering build image User stage"
    - echo "Tag $VERSION"
    - echo "Node Env $NODE_ENV"
    - echo $ALICLOUD_ACR_PASSWORD | docker login --username=h7-cicd@5261184160278876 hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com --password-stdin
    - docker build -f ./dockerfiles/hiseven.dockerfile
      --tag hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com/baymax/elfproxy:$VERSION
      --build-arg ENV=$NODE_ENV --build-arg VERSION=$VERSION
      --build-arg ALICLOUD_ACCESS_KEY_ID=$ALICLOUD_ACCESS_KEY --build-arg ALICLOUD_SECRET_ACCESS_KEY=$ALICLOUD_SECRET_KEY --build-arg ALICLOUD_DEFAULT_REGION=$ALICLOUD_REGION --build-arg ALICLOUD_ENDPOINT='oss-ap-southeast-1.aliyuncs.com'
      --no-cache .
    # - docker login --username=h7-cicd@5261184160278876 --password=$ALICLOUD_ACR_PASSWORD hiseven-registry.ap-southeast-1.cr.aliyuncs.com
    - docker push hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com/baymax/elfproxy:$VERSION
  environment:
    name: $ENV
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^(feat|bug)\//
      variables:
        ENV: staging
        NODE_ENV: stg
        VERSION: $CI_COMMIT_SHORT_SHA
      when: manual
    - if: $CI_COMMIT_BRANCH == 'master'
      variables:
        ENV: staging
        NODE_ENV: stg
        VERSION: $CI_COMMIT_SHORT_SHA
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      variables:
        ENV: production
        NODE_ENV: prod
        VERSION: $CI_COMMIT_TAG

tf-validate-hiseven:
  stage: tf-validate-hiseven
  script:
    - echo ENV $ENV
    - terraform -chdir=./.deploy/terraform init
    - terraform -chdir=./.deploy/terraform validate
  environment:
    name: $ENV
  needs: [build-image-hiseven]
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
      changes:
        - .deploy/terraform/**/*
      variables:
        ENV: staging
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      changes:
        - .deploy/terraform/**/*
      variables:
        ENV: production

tf-plan-hiseven:
  stage: tf-plan-hiseven
  script:
    - echo ALICLOUD_ACCESS_KEY $ALICLOUD_ACCESS_KEY
    - echo ALICLOUD_SECRET_KEY $ALICLOUD_SECRET_KEY
    - echo ENV $ENV
    - terraform -chdir=./.deploy/terraform init
    - terraform -chdir=./.deploy/terraform workspace select -or-create $ENV
    - terraform -chdir=./.deploy/terraform workspace list
    - terraform -chdir=./.deploy/terraform plan
      -var image_tag=$CI_COMMIT_SHORT_SHA
      -var service_name=$SERVICE_NAME
      --var-file=$ENV.tfvars
      -out "planfile"
  environment:
    name: $ENV
  needs: [tf-validate-hiseven]
  artifacts:
    paths:
      - ./.deploy/terraform/planfile
    reports:
      dotenv: build.env
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
      changes:
        - .deploy/terraform/**/*
      variables:
        ENV: staging
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      changes:
        - .deploy/terraform/**/*
      variables:
        ENV: production

tf-apply-hiseven:
  stage: tf-apply-hiseven
  script:
    - echo ALICLOUD_ACCESS_KEY $ALICLOUD_ACCESS_KEY
    - echo ALICLOUD_SECRET_KEY $ALICLOUD_SECRET_KEY
    - echo ENV $ENV
    - terraform -chdir=./.deploy/terraform init
    - terraform -chdir=./.deploy/terraform workspace select $ENV
    - terraform -chdir=./.deploy/terraform workspace list
    - terraform -chdir=./.deploy/terraform apply -input=false "planfile"
  environment:
    name: $ENV
  needs: [tf-plan-hiseven]
  artifacts:
    reports:
      dotenv: build.env
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
      changes:
        - .deploy/terraform/**/*
      variables:
        ENV: staging
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      changes:
        - .deploy/terraform/**/*
      variables:
        ENV: production

rolling-update-hiseven:
  stage: rolling-update-hiseven
  needs: [build-image-hiseven]
  image: public.ecr.aws/h8h1e3y7/aliyuncli:3.0.209-v1
  script:
    - echo "Entering rolling update User stage"
    - echo "Tag $VERSION"
    - echo "Env $ENV"
    - aliyun configure set --profile default --mode AK --region ap-southeast-1 --access-key-id ${ALICLOUD_ACCESS_KEY} --access-key-secret ${ALICLOUD_SECRET_KEY}
    - . ./.deploy/rollingupdate-hiseven.sh
  resource_group: $ENV
  environment:
    name: $ENV
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^(feat|bug)\//
      variables:
        ENV: staging
        VERSION: $CI_COMMIT_SHORT_SHA
    - if: $CI_COMMIT_BRANCH == 'master'
      variables:
        ENV: staging
        VERSION: $CI_COMMIT_SHORT_SHA
    - if: $CI_COMMIT_TAG =~ /^hi7-v(?:\d+.){2}(?:\d+)$/
      variables:
        ENV: production
        VERSION: $CI_COMMIT_TAG
