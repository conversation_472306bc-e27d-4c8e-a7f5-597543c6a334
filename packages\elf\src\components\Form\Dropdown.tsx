import DownIcon from '@hi7/assets/icon/chevron-down.svg';
import Helper from '@hi7/components/Form/Helper';
import Label from '@hi7/components/Form/Label';
import { DEFAULT_INPUT_TYPE } from '@hi7/configs/input';
import { getInputClasses } from '@hi7/helpers/class';
import {
  getHelperOrder,
  getInputOrder,
  getLabelOrder,
} from '@hi7/helpers/input';
import type { InputProps, InputType } from '@hi7/interface/input';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useController, useFormContext } from 'react-hook-form';

type DropdownProps = InputProps & {
  options: { value: string; label: string }[];
};

const INPUT_STYLE: Record<InputType, string> = {
  Outlined: '',
  Standard: 'caret-transparent',
};

const Dropdown: React.FC<DropdownProps> = ({
  label,
  name,
  required,
  options,
  variant = DEFAULT_INPUT_TYPE,
}) => {
  const { control, trigger } = useFormContext();
  const { field } = useController({ name, control });
  const [isFocused, setIsFocused] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [inputValue, setInputValue] = useState(field.value || '');

  useEffect(() => {
    if (!field.value) setInputValue('');
  }, [field.value]);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  const handleBlur = () => {
    trigger(name);
    field.onBlur();
  };

  const handleFocus = () => {
    setIsFocused(true);
    setShowOptions(true);
  };

  const handleClick = () => {
    setIsFocused((prev) => !prev);
    setShowOptions((prev) => !prev);
  };

  const handleOptionClick = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>,
    option: { value: string; label: string },
  ) => {
    e.preventDefault();
    setInputValue(option.label);
    field.onChange(option.value);
    trigger(name);
    setShowOptions(false);
  };

  const handleClickOutside = useCallback((e: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node))
      closeOptionFocus();
  }, []);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Tab' || e.key === 'Escape') closeOptionFocus();
  }, []);

  const closeOptionFocus = () => {
    setShowOptions(false);
    setIsFocused(false);
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleClickOutside, handleKeyDown]);

  return (
    <>
      <div className="relative mb-6 flex flex-col" ref={dropdownRef}>
        <div className={`hi7-dropdown relative ${getInputOrder()}`}>
          <input
            {...field}
            id={name}
            type="text"
            value={inputValue}
            onBlur={handleBlur}
            className={`${getInputClasses({ variant, value: inputValue })} pr-10 ${INPUT_STYLE[variant]}`}
            onFocus={handleFocus}
            autoComplete={`new-${name}`}
            onMouseDown={handleClick}
            readOnly
          />
          <span className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 transform">
            <DownIcon className={showOptions ? 'rotate-180' : ''} />
          </span>
          {showOptions && (
            <div className="absolute z-10 mt-1 max-h-60 w-full overflow-y-auto border border-gray-300 bg-white shadow-md">
              {options.map((option) => (
                <div
                  key={option.value}
                  onClick={(e: React.MouseEvent<HTMLDivElement, MouseEvent>) =>
                    handleOptionClick(e, option)
                  }
                  className="cursor-pointer p-2 hover:bg-gray-200"
                >
                  {option.label}
                </div>
              ))}
            </div>
          )}
        </div>
        <Label
          name={name}
          required={required}
          isFocused={isFocused}
          variant={variant}
          value={field.value}
          classes={getLabelOrder()}
        >
          {label}
        </Label>
        <Helper name={name} classes={getHelperOrder()} />
      </div>
    </>
  );
};

export default Dropdown;
