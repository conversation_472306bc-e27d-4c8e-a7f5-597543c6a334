'use client';

import Desktop from '@hi7/components/Responsive/Desktop';
import Mobile from '@hi7/components/Responsive/Mobile';
import { isSSR } from '@hi7/helpers/ssr';
import useScreenSize from '@hi7/helpers/useScreenSize';
import type React from 'react';

type ResponsiveProps = {
  desktop: React.ReactNode;
  mobile: React.ReactNode;
};

const Responsive = ({ desktop, mobile }: ResponsiveProps) => {
  const { isMobile } = useScreenSize();
  if (isSSR()) {
    return (
      <>
        <Desktop>{desktop}</Desktop>
        <Mobile>{mobile}</Mobile>
      </>
    );
  }

  return isMobile ? <Mobile>{mobile}</Mobile> : <Desktop>{desktop}</Desktop>;
};

export default Responsive;
