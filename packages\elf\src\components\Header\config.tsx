import type { MenuLinkProps } from '@hi7/interface/link';

import P1 from './icons/p1.svg';
import P2 from './icons/p2.svg';
import P3 from './icons/p3.svg';
import P4 from './icons/p4.svg';
import P5 from './icons/p5.svg';
import P6 from './icons/p6.svg';
import P7 from './icons/p7.svg';
import R1 from './icons/r1.svg';
import R2 from './icons/r2.svg';
import S1 from './icons/s1.svg';
import S10 from './icons/s10.svg';
import S2 from './icons/s2.svg';
import S3 from './icons/s3.svg';
import S4 from './icons/s4.svg';
import S5 from './icons/s5.svg';
import S6 from './icons/s6.svg';
import S7 from './icons/s7.svg';
import S8 from './icons/s8.svg';
import S9 from './icons/s9.svg';

export const ROUTES: MenuLinkProps[] = [
  {
    url: '/proxies',
    children: 'Proxies',
    items: [
      {
        Icon: P1,
        url: '/proxies/dynamic-residential-proxy',
        text: 'Dynamic Residential Proxy',
        subText: 'Rotating real-user IPs for secure browsing',
        order: 'order-1 lg:order-1',
      },
      {
        Icon: P2,
        url: '/proxies/static-residential-proxy',
        text: 'Static Data Centre IPv4',
        subText: 'Fast, stable proxies for tasks.',
        order: 'order-5 lg:order-2',
      },
      {
        Icon: P3,
        url: '/proxies/dynamic-data-centre-proxy',
        text: 'Dynamic Data Centre Proxy',
        subText: 'High-speed, reliable proxies for data scraping',
        order: 'order-2 lg:order-3',
      },
      {
        Icon: P4,
        url: '/proxies/static-residential-isp-ipv4',
        text: 'Static Residential ISP IPv4',
        subText: 'Consistent, reliable ISP-based proxies.',
        order: 'order-6 lg:order-4',
      },
      {
        Icon: P5,
        url: '/proxies/static-residential-ipv6',
        text: 'Static Residential IPv6',
        subText: 'Stable, long-term proxies for browsing',
        order: 'order-3 lg:order-5',
      },
      {
        Icon: P6,
        url: '/proxies/static-data-centre-ipv4-pro',
        text: 'Static Data Centre IPv4 PRO',
        subText: 'High-performance, stable data centre proxies.',
        order: 'order-7 lg:order-6',
      },
      {
        Icon: P7,
        url: '/proxies/static-residential-ipv4',
        text: 'Static Residential IPv4',
        subText: 'Reliable, long-term proxies for browsing',
        order: 'order-4 lg:order-7',
      },
    ],
  },
  {
    url: '/pricing',
    children: 'Pricing',
  },
  {
    url: '/solutions',
    children: 'Solutions',
    items: [
      {
        Icon: S1,
        url: '/solutions/social-media-matrix',
        text: 'Social Media Matrix',
        subText: 'Manage multiple accounts with unique IPs',
        order: 'order-1 lg:order-1',
      },

      {
        Icon: S2,
        url: '/solutions/e-commerce',
        text: 'E-commerce',
        subText: 'Deliver geo-based offers and monitor competitors',
        order: 'order-6 lg:order-2',
      },

      {
        Icon: S3,
        url: '/solutions/market-research',
        text: 'Market Research',
        subText: 'Unlock Audience Insights with ElfProxy',
        order: 'order-2 lg:order-3',
      },

      {
        Icon: S4,
        url: '/solutions/brand-protection',
        text: 'Brand Protection',
        subText: 'Monitor competitors and protect your brand',
        order: 'order-7 lg:order-4',
      },

      {
        Icon: S5,
        url: '/solutions/web-browsing',
        text: 'Web Browsing',
        subText: 'Bypass Geo-Blocks Securely & Fast',
        order: 'order-3 lg:order-5',
      },

      {
        Icon: S6,
        url: '/solutions/website-security-testing',
        text: 'Website Security Testing',
        subText: 'Identify Traffic Sources & Security Gaps Effortlessly',
        order: 'order-8 lg:order-6',
      },
      {
        Icon: S7,
        url: '/solutions/ad-advertising',
        text: 'Ad Verification',
        subText: 'Precision Audience Segmentation by Location',
        order: 'order-4 lg:order-7',
      },

      {
        Icon: S8,
        url: '/solutions/flash-sales',
        text: 'Flash Sales',
        subText: 'Bypass limits with ElfProxy to cop more trendy brands',
        order: 'order-9 lg:order-8',
      },

      {
        Icon: S9,
        url: '/solutions/web-scraping',
        text: 'Web Scraping',
        subText: 'Hide your IP to scrape safely and stay undetected',
        order: 'order-5 lg:order-9',
      },

      {
        Icon: S10,
        url: '/solutions/seo-monitoring',
        text: 'SEO Monitoring',
        subText: 'Uncover keywords, refine traffic, and boost security',
        order: 'order-10 lg:order-10',
      },
    ],
  },
  {
    url: '/resource',
    children: 'Resource',
    items: [
      {
        Icon: R1,
        url: '/resource/faq',
        text: 'FAQs',
        subText: 'Frequently asked questions and answers',
      },
      {
        Icon: R2,
        url: 'https://api.elfproxy.com/doc.html#/home',
        text: 'API',
        subText: 'Visit our documentation',
      },
    ],
  },
  {
    url: 'https://blog.elfproxy.com/',
    children: 'Industry News',
  },
  {
    asButton: true,
    url: 'https://login.elfproxy.com/dashboard',
    children: `${'Sign Up'}/${'Log In'}`,
  },
] as const;
