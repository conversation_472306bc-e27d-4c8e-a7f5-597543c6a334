'use client';

import Logo2 from '@hi7/assets/logo/logo-2.svg';
import Logo from '@hi7/assets/logo/logo.svg';
import LocaleSwitcher from '@hi7/components/LocaleSwitcher';
import MenuLink from '@hi7/components/MenuLink';
import useOutsideAlerter from '@hi7/helpers/useOutsideAlerter';
import useScroll from '@hi7/helpers/useScroll';
import { useHeader } from '@hi7/provider/HeaderProvider';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import { useRef } from 'react';
import Link from '../Link';
import { ROUTES } from './config';

const DARK_THEME_URL = [
  '/resource/faq',
  '/industry-news',
  '/industry-news/',
  '/solutions/',
  '/proxies/static-residential-proxy',
  '/proxies/static-residential-ipv6',
  '/proxies/static-residential-ipv4',
];

const Desktop = () => {
  const isWindowScrolled = useScroll();
  const pathname = usePathname();
  const isDarkTheme = DARK_THEME_URL.some((url) => pathname.includes(url));

  const { closeMenu } = useHeader();
  const wrapperRef = useRef(null);
  useOutsideAlerter(wrapperRef, () => {
    closeMenu();
  });

  const showDisplayWhiteBackground = isWindowScrolled;
  const showDisplayWhiteText = !isWindowScrolled && !isDarkTheme;

  return (
    <nav
      ref={wrapperRef}
      className={clsx(
        'fixed top-0 z-30 flex h-[78px] w-screen min-w-screen transform items-center justify-between px-30 py-4 transition-all duration-200',
        // isDarkTheme ? 'text-[#1E1E1E]' : 'text-white',
        showDisplayWhiteBackground ? 'bg-[#FFF]' : 'bg-transparent',
        showDisplayWhiteText ? 'text-white' : 'text-[#1E1E1E]',
      )}
    >
      <div className="flex h-full">
        <Link url={''}>
          {showDisplayWhiteText ? (
            <Logo width="100%" height="100%" />
          ) : (
            <Logo2 width="100%" height="100%" />
          )}
        </Link>
      </div>

      <div className="flex items-center gap-7.5 text-[14px] xl:text-[16px]">
        {ROUTES.map((route, index) => (
          <MenuLink key={index} {...route} />
        ))}

        <div className="z-50">
          <LocaleSwitcher />
        </div>
      </div>
    </nav>
  );
};

export default Desktop;
