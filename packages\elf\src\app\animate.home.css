@theme inline {
  --client-animation-duration: 46s;
}

@theme {
  --animate-home-client-cat: homeClientCat var(--client-animation-duration)
    ease-in-out infinite;
  @keyframes homeClientCat {
    0% {
      transform: translateX(10dvw);
    }

    100% {
      transform: translateX(-100dvw);
    }
  }

  --animate-home-client-cat-bg: homeClientCatBg var(--client-animation-duration)
    ease-in-out infinite;
  @keyframes homeClientCatBg {
    0% {
      transform: translateX(10dvw);
    }

    100% {
      transform: translateX(-100dvw);
    }
  }

  --animate-home-client-cat-bg-2: homeClientCatBg
    var(--client-animation-duration) var(--client-animation-duration)
    ease-in-out infinite;
}
