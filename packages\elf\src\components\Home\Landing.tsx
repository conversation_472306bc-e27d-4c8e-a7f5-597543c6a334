'use client';

import { useI18n } from '@hi7/lib/i18n';

import landingBackgroundMobile from '@hi7/assets/lotties/landing-background-mobile.png';
import landingBackgroundDesktop from '@hi7/assets/lotties/landing-background.png';
import { useGaConversion } from '@hi7/helpers/google-ads';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import Image from 'next/image';
import LinkButton from '../Form/LinkButton';

function Landing() {
  const invokeTGConversion = useGaConversion({
    conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_TG_CONVERSION_LABEL,
  });
  const invokeWAConversion = useGaConversion({
    conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_WA_CONVERSION_LABEL,
  });

  const locale = useLocale();
  const t = useI18n();
  const { isMobile } = useScreenSize();
  const redirectUrl = 'https://007tg.com/ccs/elfproxy';

  const handleConversionClick = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    e.preventDefault();
    invokeTGConversion();
    invokeWAConversion();

    setTimeout(() => {
      window.open(redirectUrl, '_blank');
    }, 300);
  };

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-[#002035] lg:min-h-(--min-h-hvh) lg:rounded-br-[100px] lg:rounded-bl-[100px] lg:pt-[78px]">
          <div
            className={`absolute bottom-0 ${isMobile ? 'right-0 left-0' : 'right-[-70%] left-[-70%] lg:right-0 lg:left-0'}`}
          >
            <div
              className={`absolute bottom-0 w-full ${isMobile ? 'flex justify-center' : ''}`}
            >
              {isMobile ? (
                <Image
                  src={landingBackgroundMobile}
                  alt=""
                  height={1920}
                  width={1080}
                  priority
                  className="object-contain"
                />
              ) : (
                <Image
                  src={landingBackgroundDesktop}
                  alt=""
                  height={2160}
                  width={3840}
                  priority
                  className="object-cover"
                />
              )}
            </div>
          </div>

          <div className="relative flex flex-col items-center justify-center px-5 py-11 text-white lg:pt-[30px]">
            <div className="text-center lg:w-[60dvw] lg:min-w-[855px]">
              <h1 className="mb-[14px] flex flex-col text-[46px] leading-none lg:text-[64px]">
                <span className={clsx(locale === 'zh' && 'order-2')}>
                  {t('Your ')}
                  <span className="font-orbitron inline-flex font-bold text-[#31F4A0] lg:inline-block">
                    {t('Top IP Proxy ')}
                  </span>
                  {t(' Expert ')}
                </span>
                <span className="block text-[24px] leading-[36px] lg:text-[32px] lg:leading-[60px]">
                  {t('for Social Media Operations')}
                </span>
              </h1>
              <h2
                className={clsx(
                  'mb-[14px] text-[18px] leading-[25.2px] lg:whitespace-pre-line',
                  locale === 'en' ? 'pb-[2dvh]' : 'py-[2dvh]',
                )}
              >
                {t(
                  'Empowering every step of your social media campaigns with advanced business metrics and\ncutting-edge research for guaranteed wins!',
                )}
              </h2>

              <div className="m-auto grid max-w-[80%] grid-cols-1 gap-4 text-center lg:flex lg:flex-row lg:items-center lg:justify-center">
                <LinkButton
                  type="main"
                  size="L"
                  className="justify-center"
                  url={redirectUrl}
                  target="_blank"
                  onClick={handleConversionClick}
                >
                  {t('Free Trial')}
                </LinkButton>
                <LinkButton
                  type="secondary"
                  size="L"
                  className="justify-center"
                  url={redirectUrl}
                  target="_blank"
                  onClick={handleConversionClick}
                >
                  {t('Buy Now from $0.3/month')}
                </LinkButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Landing;
