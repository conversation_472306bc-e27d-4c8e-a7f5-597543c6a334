import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Static Residential IP Proxy - Relay IP/Accelerated IP - Global Network Latency 20-80ms',
    ),
    description: t(locale)(
      'Global pure residential IP free trial, with relay acceleration achieving 20-80ms latency, comparable to an international dedicated line, each IP dedicated for exclusive use. Ideal for high-stability, low-latency businesses: enterprise operations, cross-border e-commerce, TikTok, and more. 99.9% online stability, with high-purity IPs filtered based on your business needs.',
    ),
    keywords: t(locale)(
      'Dedicated Line Relay, Node Relay, Dedicated Line Acceleration, Relay IP, Accelerated IP, Residential IP Relay, IP Latency',
    ),
  };
};

function Proxy() {
  return <ProxyTemplate id="proxy-7" />;
}

export default Proxy;
