import { getCanonicalURL } from './getCanonical';

/**
 * Open Graph meta tags configuration
 */
export interface OpenGraphConfig {
  title: string;
  description: string;
  url: string;
  images?: string[];
  siteName?: string;
  locale?: string;
  type?: 'website' | 'article' | 'product' | 'profile';
  article?: {
    publishedTime?: string;
    modifiedTime?: string;
    author?: string;
    section?: string;
    tags?: string[];
  };
}

type OpenGraphMeta = {
  title?: string;
  description?: string;
  url?: string;
  images?: (
    | {
        url: string;
        width?: number;
        height?: number;
        alt?: string;
      }
    | string
  )[];
  siteName?: string;
  locale?: string;
  type?: 'website' | 'article' | 'product' | 'profile';
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  section?: string;
  tags?: string[];
};

/**
 * Generates Open Graph meta tags for Next.js metadata
 * @param config Open Graph configuration
 * @returns Open Graph metadata object
 */
export function generateOpenGraphMeta(config: OpenGraphConfig) {
  const {
    title,
    description,
    url,
    images,
    siteName = 'ElfProxy',
    locale = 'en_US',
    type = 'website',
    article,
  } = config;

  const domain = getCanonicalURL();

  const absoluteImageUrls = images?.map((image) =>
    image.startsWith('http') ? image : `${domain}${image}`,
  );

  const openGraph: OpenGraphMeta = {
    title,
    description,
    url,
    images: absoluteImageUrls,
    siteName,
    locale,
    type,
  };

  if (type === 'article' && article) {
    openGraph.publishedTime = article.publishedTime;
    openGraph.modifiedTime = article.modifiedTime;
    openGraph.authors = article.author ? [article.author] : undefined;
    openGraph.section = article.section;
    openGraph.tags = article.tags;
  }

  return {
    openGraph,
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: absoluteImageUrls,
      site: '@ElfProxy',
      creator: '@ElfProxy',
    },
  };
}
