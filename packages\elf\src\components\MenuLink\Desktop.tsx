'use client';

import Link from '@hi7/components/Link';
import type { MenuLinkProps } from '@hi7/interface/link';
import { useI18n } from '@hi7/lib/i18n';
import { useHeader } from '@hi7/provider/HeaderProvider';
import LinkButton from '../Form/LinkButton';
import SubMenuLink from '../SubMenuLink';

const MenuLink = (props: MenuLinkProps) => {
  const { url, asButton: button, onClick, children, items = [] } = props;
  const t = useI18n();
  const { closeMenu } = useHeader();

  const hasSubitem = items.length > 0;
  if (hasSubitem) {
    return <SubMenuLink {...props} />;
  }

  if (button) {
    return (
      <LinkButton
        target="_blank"
        type="main"
        size="M"
        url={url}
        onClick={(e) => {
          onClick?.(e);
          closeMenu();
        }}
      >
        {t(children as string)}
      </LinkButton>
    );
  }

  return (
    <Link
      onClick={(e) => {
        onClick?.(e);
        closeMenu();
      }}
      url={url}
      className="relative flex cursor-pointer items-center gap-2.5"
    >
      {t(children as string)}
    </Link>
  );
};

export default MenuLink;
