'use client';

import DropDown from '@hi7/assets/icon/dropdown.svg';
import Globe from '@hi7/assets/icon/globe.svg';
import COOKIES from '@hi7/configs/cookies';
import { i18n, type Locale } from '@hi7/lib/i18n';
import { useHeader } from '@hi7/provider/HeaderProvider';
import { useLocale } from '@hi7/provider/LocaleProvider';
import { useGlobalStore } from '@hi7/provider/ZustandContext';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import type { MouseEvent } from 'react';
import { DISPLAY_LANG, LANGUAGE_NAME } from './config';

export default function LocaleSwitcher() {
  const router = useRouter();
  const setBaseurl = useGlobalStore((s) => s.setBaseurl);
  const { activeMenu, openMenu, closeMenu } = useHeader();
  const currentLocale = useLocale();

  const handleLocaleChange = (
    e: MouseEvent<HTMLButtonElement>,
    locale: Locale,
  ) => {
    e.stopPropagation();
    // Set locale in cookie
    document.cookie = `${COOKIES.localization}=${locale}; path=/; max-age=${365 * 24 * 60 * 60}`;

    // Update baseurl (now empty since no locale prefix)
    setBaseurl('');

    // Refresh the page to apply new locale
    router.refresh();
    closeMenu();
  };

  return (
    <div
      onClick={() => openMenu('locale')}
      className={clsx('relative flex cursor-pointer items-center gap-2.5')}
    >
      <Globe />
      <div className="flex items-center gap-[10px]">
        <div className="flex items-center gap-[10px]">
          {DISPLAY_LANG[currentLocale] ?? currentLocale}
          <DropDown />
        </div>
      </div>

      {activeMenu === 'locale' && (
        <div
          className={clsx(
            'absolute top-full left-0 z-50 mt-7 grid h-fit w-fit rounded-[10px] bg-white',
            'shadow-[0_10px_15px_-3px_rgba(0,0,0,0.1),0_4px_6px_-2px_rgba(0,0,0,0.05),0_-1px_2px_rgba(0,0,0,0.03),-10px_0_20px_-5px_rgba(0,0,0,0.08),10px_0_20px_-5px_rgba(0,0,0,0.08)]',
          )}
        >
          {i18n.locales.map((locale) => (
            <button
              key={`locale-${locale}`}
              onClick={(e) => handleLocaleChange(e, locale)}
              className={clsx(
                'flex items-center gap-2 whitespace-nowrap',
                'cursor-pointer text-start text-black',
                'px-[14px] py-4',
                'transition-all duration-300',
                'mb-2 w-full min-w-[155px] rounded-[5px] bg-white hover:text-[#FFC525]',
              )}
            >
              {LANGUAGE_NAME[locale] ?? locale}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
