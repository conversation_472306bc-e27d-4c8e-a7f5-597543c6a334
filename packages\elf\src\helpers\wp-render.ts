import { TAG_MAP_NAME, TAG_MAP_VALUE } from '@hi7/configs/tag';

export const getTags = (tagIds: number[]): string[] => {
  return tagIds
    .map((tagId) => TAG_MAP_NAME[tagId] || '')
    .filter((tag) => tag !== ''); // Remove empty strings if any
};

export const getTagValues = (tagIds: number[]): string[] => {
  return tagIds.map((tagId) => TAG_MAP_VALUE[tagId] || 'general');
};

export const getCategories = (catIds: number[]): string[] => {
  return catIds
    .map((catId) => {
      switch (catId) {
        case 5:
          return 'en';
        // Add more cases for different category IDs
        default:
          return ''; // or any default placeholder if the catId doesn't match
      }
    })
    .filter((cat) => cat !== ''); // Remove empty strings if any
};
