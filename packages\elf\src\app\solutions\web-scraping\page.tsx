import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Best Web Scraping Proxy - 99.7% Scraping Success Rate - Global 100 Million Residential IP Pool.',
    ),
    description: t(locale)(
      'ElfProxy provides high-anonymity residential proxy services, featuring a vast network across more than 220 countries and cities with both dynamic and static IP options. Our solution is designed for large-scale concurrent data collection, effectively bypassing anti-scraping measures on major platforms like Amazon and TikTok. It resolves issues such as IP blocking and CAPTCHA verification, achieving an impressive 99.7% success rate in data collection. Start your free trial today!',
    ),
    keywords: t(locale)(
      'web data scraping, web data collection, crawler proxy IP, data collection IP, high-anonymity proxy IP, dynamic IP, global IP pool, competitor data monitoring',
    ),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://elfproxy.com'}/solutions/web-scraping`,
    },
  };
};

function Solution() {
  return <SolutionTemplate id="solution-5" />;
}

export default Solution;
