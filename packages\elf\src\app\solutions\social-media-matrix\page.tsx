import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Best Social Media Proxies - Real Residential IPs for Safe Multi-Account Marketing',
    ),
    description: t(locale)(
      'ElfProxy provides high-quality residential proxy services with genuine IPs from real home broadband networks across 220+ countries. Our proxies effectively hide your real IP and encrypt data transmission, ensuring your privacy and security. Overcome geo-restrictions to access social media platforms like TikTok, Facebook, and more. Manage multiple accounts with independent IPs, preventing association and enhancing safety. Solve common issues such as TikTok registration failures, zero video views, and Facebook mass bans. Our local native IPs ensure more accurate and increased local traffic delivery. Start your free trial now!',
    ),
    keywords: t(locale)(
      'TikTok Registration, TikTok Registration Issues, TikTok Proxy IP, Facebook Proxy IP, Instagram Proxy IP, Twitter Proxy IP, LinkedIn Proxy IP, YouTube Proxy IP',
    ),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com'}/solutions/social-media-matrix`,
    },
  };
};

function Solution() {
  return <SolutionTemplate id="solution-1" />;
}

export default Solution;
