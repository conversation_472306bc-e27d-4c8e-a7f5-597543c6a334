type BlockProps = {
  block: string;
  behavior?: 'auto' | 'instant' | 'smooth';
};

export const scrollToBlock = ({ block, behavior = 'smooth' }: BlockProps) => {
  const element = document.getElementById(block);
  if (element) {
    element.scrollIntoView({ behavior });
  }
};

export const scrollIntoViewWithOffset = (
  element: Element | null,
  offset = 0,
) => {
  if (!element) {
    return;
  }

  window.scrollTo({
    behavior: 'smooth',
    top:
      element.getBoundingClientRect().top -
      document.body.getBoundingClientRect().top -
      offset,
  });
};

export const isElementInView = (
  element: Element | null,
  offset?: { top?: number; left?: number },
): boolean => {
  if (!element) {
    return false;
  }

  const { top = 0, left = 0 } = offset ?? {};

  const rect = element.getBoundingClientRect();

  return (
    rect.top >= top &&
    rect.left >= left &&
    rect.bottom <=
      (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};
