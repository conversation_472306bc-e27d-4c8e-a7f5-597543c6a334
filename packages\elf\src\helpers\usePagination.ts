import usePaginationStore from '@hi7/lib/stores/blogPaginationStore';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo } from 'react';

export const usePagination = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Use selector pattern to minimize re-renders
  const {
    currentPage,
    itemsPerPageOptions,
    selectedItemsPerPage,
    setCurrentPage,
    setSelectedItemsPerPage,
  } = usePaginationStore(
    useCallback(
      (state) => ({
        currentPage: state.currentPage,
        itemsPerPageOptions: state.itemsPerPageOptions,
        selectedItemsPerPage: state.selectedItemsPerPage,
        setCurrentPage: state.setCurrentPage,
        setSelectedItemsPerPage: state.setSelectedItemsPerPage,
      }),
      [],
    ),
  );

  // Memoize URL parameter parsing
  const urlParams = useMemo(
    () => ({
      page: Number(searchParams.get('page')) || 1,
      perPage: Number(searchParams.get('perPage')) || selectedItemsPerPage,
    }),
    [searchParams, selectedItemsPerPage],
  );

  // Initialize state from URL parameters
  useEffect(() => {
    const { page, perPage } = urlParams;

    if (page !== currentPage) {
      setCurrentPage(page);
    }
    if (perPage !== selectedItemsPerPage) {
      setSelectedItemsPerPage(perPage);
    }
  }, [
    urlParams,
    currentPage,
    selectedItemsPerPage,
    setCurrentPage,
    setSelectedItemsPerPage,
  ]);

  // Memoize URL update function
  const updateURL = useCallback(
    (newPage: number, newPerPage: number = selectedItemsPerPage) => {
      const params = new URLSearchParams(searchParams);
      params.set('page', newPage.toString());
      params.set('perPage', newPerPage.toString());

      router.push(`${pathname}?${params.toString()}#blogposts`);
    },
    [router, pathname, searchParams, selectedItemsPerPage],
  );

  // Memoize handlers
  const handlePageChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
      updateURL(page);
    },
    [setCurrentPage, updateURL],
  );

  const handleItemsPerPageChange = useCallback(
    (perPage: number) => {
      setSelectedItemsPerPage(perPage);
      const newPage = 1;
      setCurrentPage(newPage);
      updateURL(newPage, perPage);
    },
    [setSelectedItemsPerPage, setCurrentPage, updateURL],
  );

  // Memoize return value to prevent unnecessary re-renders
  return useMemo(
    () => ({
      currentPage,
      itemsPerPageOptions,
      selectedItemsPerPage,
      handlePageChange,
      handleItemsPerPageChange,
    }),
    [
      currentPage,
      itemsPerPageOptions,
      selectedItemsPerPage,
      handlePageChange,
      handleItemsPerPageChange,
    ],
  );
};
