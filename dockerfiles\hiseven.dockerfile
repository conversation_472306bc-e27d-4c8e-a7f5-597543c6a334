FROM hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com/base/gitlab/aliyuncli:node AS builder

WORKDIR /app

ARG VERSION=unknown \
    ENV=stg \
    ALICLOUD_ENDPOINT \
    ALICLOUD_ACCESS_KEY_ID \
    ALICLOUD_SECRET_ACCESS_KEY \
    ALICLOUD_DEFAULT_REGION \
    NPM_TOKEN \

ENV VERSION=$VERSION \
    ENV=$ENV \
    NPM_TOKEN=$NPM_TOKEN \
    ASSET_PREFIX=https://media.hiseven.com/elf

COPY . .

RUN ls -l packages/elf/auto
RUN chmod +x packages/elf/auto/build

RUN pnpm install
RUN pnpm elf:build:$ENV
RUN ossutil64 config --access-key-id $ALICLOUD_ACCESS_KEY_ID --access-key-secret $ALICLOUD_SECRET_ACCESS_KEY --endpoint $ALICLOUD_ENDPOINT
RUN ossutil64 cp -r -u packages/elf/.next/static oss://assets-hiseven/elf/_next/static

FROM node:20-alpine as runtime

WORKDIR /app

COPY --from=builder /app/packages/elf/.next/standalone/packages/elf ./
COPY --from=builder /app/packages/elf/.next/standalone ./

RUN rm -rf /app/packages

EXPOSE 3000
CMD ["sh", "-c", "node server.js"]
