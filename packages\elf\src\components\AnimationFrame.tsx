'use client';

import type { VariantType } from '@hi7/configs/animation';
import { defaultAnimateConfig } from '@hi7/configs/animation';
import useScreenSize from '@hi7/helpers/useScreenSize';
import clsx from 'clsx';
import { motion } from 'motion/react';
import type React from 'react';

interface AnimationFrameProps {
  variant: VariantType;
  once?: boolean;
  className?: string;
  children: React.ReactNode;
  margin?: string;
}

const AnimationFrame: React.FC<AnimationFrameProps> = ({
  variant,
  children,
  className,
  once = true,
  margin,
}) => {
  const { isMobile } = useScreenSize();
  const { initial, animate, transition } = defaultAnimateConfig[variant];

  return isMobile ? (
    <div className={clsx('lg:hidden', className)}>{children}</div>
  ) : (
    <motion.div
      initial={initial}
      whileInView={animate}
      viewport={{ once, margin }}
      transition={transition}
      className={clsx('hidden lg:flex', className)}
    >
      {children}
    </motion.div>
  );
};

export default AnimationFrame;
