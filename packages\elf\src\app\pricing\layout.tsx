import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Buy Dynamic IP/Static IP - Buy Static Residential IP - ElfProxy Global Proxy IP',
    ),
    description: t(locale)(
      'ElfProxy offers competitive proxy IP prices with no real-name verification required for purchase and trial. We provide data center IPs, residential IPs, native IPs, SOCKS5 proxies, HTTP(s) proxies, dedicated IPs, and dynamic proxy pools. Our services support daily and pay-as-you-go pricing models.',
    ),
    keywords: t(locale)(
      'Buy IP, Buy Dynamic IP, Buy Static IP, Buy Residential IP, Buy Data Center IP, Buy Static Residential IP, Buy SOCKS5 Proxy, Buy HTTP Proxy',
    ),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com'}/pricing`,
    },
  };
};

export default function PricingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
