import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Best Market Research Proxy - 99.9% Data Accuracy - Global 100 Million Residential IPs Anti-Ban',
    ),
    description: t(locale)(
      'ElfProxy offers high-anonymity residential proxy IP services with global residential IP resources from over 220 countries and cities. It supports competitor monitoring, consumer behavior analysis, and public opinion tracking. By overcoming anti-crawling mechanisms, it achieves a data collection accuracy rate of up to 99.9%, providing a scientific basis for market prediction.',
    ),
    keywords: t(locale)(
      'Market Research Proxy IP, Global Market Survey, Data Collection IP, Competitor Data Monitoring, Market Research, Dynamic Residential IP, Dynamic IP, Global Residential IP',
    ),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com'}/solutions/market-research`,
    },
  };
};

function Solution() {
  return <SolutionTemplate id="solution-2" />;
}

export default Solution;
