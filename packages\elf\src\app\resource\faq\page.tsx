'use client';

import Minus from '@hi7/assets/icon/minus.svg';
import Plus from '@hi7/assets/icon/plus.svg';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { useState } from 'react';
import { FAQS } from './config';

function FAQ() {
  const [selected, setSelected] = useState(0);
  const t = useI18n();

  return (
    <div className="flex items-center justify-center">
      <div className="w-full">
        <div className="relative pb-[100px] text-[#002035] lg:pt-[78px] lg:pb-[120px]">
          <div className="relative flex flex-col justify-center px-5 py-11 lg:items-center lg:pt-[60px]">
            <div className="mb-[40px] text-[18px] lg:w-[50dvw] lg:min-w-[700px] lg:text-center">
              <h1 className="mb-[16px] text-[46px] leading-none lg:text-[64px]">
                {t('FAQs')}
              </h1>
              <p>{t('All your questions, answered!')}</p>
              <p>
                {t(
                  'If you are missing any Information, do not hesitate to drop us a message.',
                )}
              </p>
            </div>
            <div className="flex flex-col gap-2 lg:w-[60dvw] lg:min-w-[830px]">
              {FAQS.map((faq, idx) => (
                // eslint-disable-next-line react/jsx-key
                <div
                  className={clsx(
                    'flex flex-col gap-4 rounded-[8px] border p-6 transition-all hover:cursor-pointer',
                    selected === idx
                      ? 'border-[#2ECFC7] bg-[#E3FFFB]'
                      : 'border-[#D9D9D9]',
                  )}
                  onClick={() => setSelected(idx)}
                >
                  <div className="flex">
                    <b
                      className={clsx(
                        'flex-1 text-[20px] leading-[1.4] font-semibold transition-all',
                        selected === idx && 'text-[#2ECFC7]',
                      )}
                    >
                      {t(faq.title)}
                    </b>
                    {selected === idx ? <Minus /> : <Plus />}
                  </div>
                  {selected === idx && (
                    <p dangerouslySetInnerHTML={{ __html: t(faq.desc) }} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FAQ;
