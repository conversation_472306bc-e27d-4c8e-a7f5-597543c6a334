@import 'tailwindcss';
@import './animate.fab.css';
@import './animate.home.css';
@import './animate.proxy.css';

@layer base {
  body,
  html {
    scroll-behavior: smooth;
  }

  input:-webkit-autofill,
  textarea:-webkit-autofill,
  select:-webkit-autofill {
    transition: background-color 5000s ease-in-out;
  }

  input:is(:-webkit-autofill, :autofill) + label,
  .hi7-dropdown input:is(:-webkit-autofill, :autofill) + label,
  textarea:is(:-webkit-autofill, :autofill) + label {
    top: -0.938rem;
  }

  input:not([type='radio']):not([type='checkbox']),
  textarea {
    -webkit-border-radius: 0;
    border-radius: 0;
  }
}

@theme inline {
  --font-orbitron: var(--font-orbitron);
  --min-h-hvh: 100dvh;
  --min-sm-h-hvh: calc(100dvh - 56px);
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

@utility animation-delay-* {
  animation-delay: --value(integer) ms;
}
/* use for animation delay */
[data-trigger-animation],
[data-trigger-animation] * {
  animation: none !important;
}
