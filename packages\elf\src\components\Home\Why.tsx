'use client';

import HomeWhyBg1 from '@hi7/assets/background/home-why-1-1.svg';
import HomeWhyBg2 from '@hi7/assets/background/home-why-1-2.svg';
import HomeWhyCat from '@hi7/assets/icon/why-cat.svg';
import HomeWhyLottie from '@hi7/assets/lotties/home-why.json';
import { useGaConversion } from '@hi7/helpers/google-ads';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import type { UIEventHandler } from 'react';
import { useState } from 'react';
import LinkButton from '../Form/LinkButton';
import Lottie from '../Lottie';
import { MAX_DATA_1, MAX_DATA_2, WHY_DATA } from './config';

function Why() {
  const invokeTGConversion = useGaConversion({
    conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_TG_CONVERSION_LABEL,
  });
  const invokeWAConversion = useGaConversion({
    conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_WA_CONVERSION_LABEL,
  });

  const [index, setIndex] = useState(0);
  const t = useI18n();
  const locale = useLocale();

  const handleScroll: UIEventHandler = (e) => {
    const cards = e.currentTarget.firstElementChild?.children || [];

    let foundIndex = 0;
    for (let i = 0; i < cards.length; i++) {
      const rect = (cards[i] as HTMLElement).getBoundingClientRect();

      const containerRect = e.currentTarget.getBoundingClientRect();
      const thresold = 200;
      if (
        rect.right - thresold > containerRect.left &&
        rect.left - thresold < containerRect.right
      ) {
        foundIndex = i;
        break;
      }
    }
    setIndex(foundIndex);
  };

  const redirectUrl = 'https://007tg.com/ccs/elfproxy';

  const handleConversionClick = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    e.preventDefault();
    invokeTGConversion();
    invokeWAConversion();

    setTimeout(() => {
      window.open(redirectUrl, '_blank');
    }, 300);
  };

  return (
    <div className="relative w-full lg:overflow-hidden">
      <HomeWhyBg1 className="absolute top-0" />
      <video playsInline autoPlay muted loop className="relative w-dvw">
        <source
          src={`${process.env.NEXT_PUBLIC_ASSET_PREFIX || ''}/videos/home-1.mp4`}
          type="video/mp4"
        />
      </video>
      <div className="relative text-white">
        <div className="mt-[-10vw] flex flex-col items-center justify-center rounded-tl-[10vw] rounded-tr-[10vw] bg-[#002035]">
          <div className="px-[30px] lg:w-[60dvw] lg:min-w-[855px]">
            <h2 className="mb-[16px] text-center text-[36px] leading-none lg:text-[48px]">
              {t('Why Choose ')}
              <span className="font-orbitron font-semibold text-[#31F4A0]">
                {t('ElfProxy')}
              </span>
              <span className="text-[#31F4A0]">?</span>
            </h2>
            <h3 className="text-center text-[18px] leading-[25.2px]">
              {t(
                'Partnering with leading global carriers, ElfProxy delivers premium, high-quality IP resources with unmatched stability and anonymity. Our solutions guarantee high-speed, secure and efficient connectivity that powers your digital success.',
              )}
            </h3>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center bg-[#002035] pt-[40px]">
          <div
            className="no-scrollbar w-full overflow-x-auto lg:w-[66dvw] lg:min-w-[960px] lg:overflow-x-hidden"
            onScroll={handleScroll}
          >
            <div className="mb-[34px] grid w-[1660px] auto-rows-[220px] grid-cols-6 gap-[30px] px-[30px] text-center text-[18px] leading-[1.4] lg:mb-[60px] lg:w-full lg:grid-cols-3">
              {WHY_DATA.map((item, i) => (
                <div
                  key={`why1-${i}`}
                  className="relative rounded-[20px] bg-linear-[135deg,#31F4A0_0%,#002035_100%]"
                >
                  <div className="absolute top-[1px] right-[1px] bottom-[1px] left-[1px] flex flex-col items-center justify-center gap-5 rounded-[20px] bg-[#002035] p-5">
                    {item.icon}
                    <p>{t(item.title)}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="relative m-auto mb-[60px] grid w-[168px] grid-cols-6 gap-x-3 lg:hidden">
            {WHY_DATA.map((_, idx) => (
              <div
                key={`why2-${idx}`}
                className={clsx([
                  'h-[10px] w-[10px] cursor-pointer rounded-full transition-all duration-300 ease-in-out',
                  index === idx ? 'bg-[#31F4A0]' : 'bg-[#D9D9D9]',
                ])}
                onMouseEnter={() => setIndex(idx)}
              ></div>
            ))}
          </div>
          <div className="mb-[100px] flex flex-col items-center gap-4 lg:mb-[15dvh] lg:flex-row lg:justify-center">
            <LinkButton
              type="main"
              size="L"
              url={redirectUrl}
              target="_blank"
              onClick={handleConversionClick}
            >
              {t('Get 10 Free Residential IPs')}
            </LinkButton>
            <LinkButton
              type="secondary"
              size="L"
              url={redirectUrl}
              target="_blank"
              onClick={handleConversionClick}
            >
              {t('Try 5 Static IPs of Your Choice')}
            </LinkButton>
          </div>
        </div>
        <div className="bg-[#002035]">
          <div className="flex flex-col items-center justify-center px-5">
            <div className="lg:w-[65dvw] lg:min-w-[960px]">
              <h2
                className={clsx(
                  'mb-[60px] justify-center text-center text-[36px] leading-none lg:text-[48px]',
                  locale === 'zh' && 'flex flex-col',
                )}
              >
                <span className={clsx(locale === 'zh' && 'order-2')}>
                  {t('Maximise Your Social Media Success with the ')}
                </span>

                <span className={clsx(locale === 'zh' && 'block')}>
                  {t('__SKIP_en_tongguo')}
                  <span className="font-orbitron inline-block font-semibold text-[#31F4A0]">
                    {t('Right IP Strategy')}
                  </span>
                </span>
              </h2>

              <div className="mb-[40px] rounded-[10px] border border-[#2ECFC7] bg-[#31F4A0] p-6 text-center text-[20px] font-semibold text-[#002035] lg:hidden">
                {t('Elevating Your Social Media Operations')}
              </div>

              <div className="mb-[64px] flex items-center justify-center lg:mb-[60px]">
                <div className="mt-[1px] hidden min-w-[334px] rounded-[10px] border border-[#2ECFC7] bg-[#31F4A0] p-6 text-center text-[20px] font-semibold text-[#002035] lg:block">
                  {t('Elevating Your Social Media Operations')}
                </div>

                <div className="mt-[1px] hidden h-[1px] w-[40px] bg-[#31F4A0] lg:block"></div>
                <div className="relative flex flex-col gap-[20px]">
                  <div className="absolute top-[100px] bottom-[100px] w-[1px] bg-[#31F4A0] lg:top-[70px] lg:bottom-[70px]"></div>
                  {MAX_DATA_1.map((item) => (
                    <div
                      key={`md1-${item.title}`}
                      className="relative grid grid-cols-[65px_1fr] gap-[20px]"
                    >
                      <div className="h-[24px]">
                        <div className="absolute top-[50%] left-[45px] h-[24px] w-[24px] translate-y-[-50%] rounded-full bg-[#31F4A0]"></div>
                        <div className="absolute top-[50%] h-[1px] w-[60px] bg-[#31F4A0]"></div>
                      </div>
                      <div className="grid auto-rows-[200px] grid-cols-[64px_1fr] items-center gap-2.5 lg:auto-rows-[140px] lg:grid-cols-[80px_1fr]">
                        <item.Icon className="w-[64px] lg:w-[80px]" />
                        <div className="flex flex-col gap-4">
                          <b className="text-[20px] font-semibold lg:text-[24px]">
                            {t(item.title)}
                          </b>
                          <p>{t(item.desc)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-[1px] min-w-[334px] rounded-[10px] border border-[#2ECFC7] bg-[#31F4A0] p-6 text-center text-[20px] font-semibold text-[#002035] lg:hidden">
                {t('Choose the Best IP for Your Social Media')}
              </div>

              <div className="mb-[64px] flex items-center justify-center lg:mb-0">
                <div className="relative flex flex-col gap-[20px]">
                  {MAX_DATA_2.map((item) => (
                    <div
                      key={`md2-${item.title}`}
                      className="relative grid grid-cols-[65px_1fr] gap-[20px] lg:grid-cols-[1fr_65px]"
                    >
                      <div className="order-2 grid auto-rows-[250px] grid-cols-[64px_1fr] items-center gap-2.5 lg:order-1 lg:auto-rows-[140px] lg:grid-cols-[1fr_80px] lg:text-right">
                        <div className="order-2 flex flex-col gap-4 lg:order-1">
                          <b className="text-[20px] font-semibold lg:text-[24px]">
                            {t(item.title)}
                          </b>
                          <p>{t(item.desc)}</p>
                        </div>
                        <item.Icon className="order-1 w-[64px] lg:order-2 lg:w-[80px]" />
                      </div>
                      <div className="order-1 h-[24px] lg:order-2">
                        <div className="absolute top-[50%] left-[45px] h-[24px] w-[24px] translate-y-[-50%] rounded-full bg-[#31F4A0] lg:right-[45px] lg:left-auto"></div>
                        <div className="absolute top-[50%] h-[1px] w-[60px] bg-[#31F4A0] lg:right-0"></div>
                      </div>
                    </div>
                  ))}
                  <div className="absolute top-[125px] bottom-[125px] w-[1px] bg-[#31F4A0] lg:top-[70px] lg:right-0 lg:bottom-[69px]"></div>
                </div>

                <div className="mt-[1px] hidden h-[1px] w-[40px] bg-[#31F4A0] lg:block"></div>
                <div className="mt-[1px] hidden min-w-[334px] rounded-[10px] border border-[#2ECFC7] bg-[#31F4A0] p-6 text-center text-[20px] font-semibold text-[#002035] lg:block">
                  {t('Choose the Best IP for Your Social Media')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="group">
        <HomeWhyBg2 />
        <div
          className={clsx(
            'absolute bottom-[2%] left-[50%] w-[350px]',
            'translate-x-[-50%] translate-y-0 opacity-100 transition-all duration-500',
            'lg:w-0 lg:translate-y-[-90%] lg:opacity-0',
            'group-hover:translate-y-0 group-hover:rounded-none group-hover:opacity-100 group-hover:lg:w-[40dvw]',
          )}
        >
          <Lottie
            animationData={HomeWhyLottie}
            masking={<HomeWhyCat width="100%" />}
          />
        </div>
      </div>
    </div>
  );
}

export default Why;
