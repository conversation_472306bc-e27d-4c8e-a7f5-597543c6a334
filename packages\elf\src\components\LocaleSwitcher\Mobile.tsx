'use client';

import clsx from 'clsx';
import { useRouter } from 'next/navigation';

import ChevronDown from '@hi7/assets/icon/chevron-down.svg';
import ChevronUp from '@hi7/assets/icon/chevron-up.svg';
import Globe from '@hi7/assets/icon/globe.svg';
import COOKIES from '@hi7/configs/cookies';
import { i18n, type Locale } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import { useGlobalStore } from '@hi7/provider/ZustandContext';
import { useState } from 'react';
import { DISPLAY_LANG, LANGUAGE_NAME } from './config';

export default function LocaleSwitcher() {
  const router = useRouter();
  const setBaseurl = useGlobalStore((s) => s.setBaseurl);
  const [isOpen, setIsOpen] = useState(false);
  const currentLocale = useLocale();

  const handleLocaleChange = (locale: Locale) => {
    // Set locale in cookie
    document.cookie = `${COOKIES.localization}=${locale}; path=/; max-age=${365 * 24 * 60 * 60}`;

    // Update baseurl (now empty since no locale prefix)
    setBaseurl('');

    // Refresh the page to apply new locale
    router.refresh();
  };

  return (
    <>
      <div
        onClick={() => setIsOpen((prev) => !prev)}
        className={'flex cursor-pointer items-center gap-2.5 py-4'}
      >
        <Globe />
        {DISPLAY_LANG[currentLocale] ?? currentLocale}
        {isOpen ? <ChevronUp /> : <ChevronDown />}
      </div>
      {isOpen &&
        i18n.locales.map((locale) => (
          <button
            key={`locale-${locale}`}
            onClick={() => {
              setIsOpen(false);
              handleLocaleChange(locale);
            }}
            className={clsx('flex cursor-pointer items-center gap-2.5 py-4')}
          >
            {LANGUAGE_NAME[locale] ?? locale}
          </button>
        ))}
    </>
  );
}
