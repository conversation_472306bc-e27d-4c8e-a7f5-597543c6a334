import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      "Static Residential IP Proxy - Dual ISP/Residential IPv6 - ElfProxy's Cost-Effective Cloud-Managed IP",
    ),
    description: t(locale)(
      'High-quality residential IPs starting at $0.3 per month, with free trial available. IPs are allocated by local ISPs, offering high anonymity and dedicated use. Ideal for WhatsApp marketing, Telegram group management, cloud control software, and more. Leverage local networks for marketing campaigns, with dual ISP support for increased stability.',
    ),
    keywords: t(locale)(
      'Static IP, Residential IP, Static Residential, Residential IPv6, Dual ISP, WhatsApp Proxy, Telegram Proxy',
    ),
  };
};

function Proxy() {
  return <ProxyTemplate id="proxy-3" />;
}

export default Proxy;
