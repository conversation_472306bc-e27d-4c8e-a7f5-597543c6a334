import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Best E-commerce Data Collection Proxy - 99.5% Scraping Success Rate - Proxy IP Anti-Ban.',
    ),
    description: t(locale)(
      'ElfProxy provides residential IP proxy services for cross-border e-commerce enterprises, with a global network covering over 220 countries and cities, allowing precise targeting of markets such as New York, USA. It enables real-time collection of product prices, reviews, and inventory data from platforms like Amazon and eBay. It solves problems like frequent CAPTCHAs and IP blocking. Start your free trial now!',
    ),
    keywords: t(locale)(
      'Cross-border e-commerce data collection, Amazon price monitoring, Amazon data collection, eBay price monitoring, eBay data collection, crawler IP, crawler proxy, e-commerce crawler IP.',
    ),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com'}/solutions/e-commerce`,
    },
  };
};

function Solution() {
  return <SolutionTemplate id="solution-6" />;
}

export default Solution;
