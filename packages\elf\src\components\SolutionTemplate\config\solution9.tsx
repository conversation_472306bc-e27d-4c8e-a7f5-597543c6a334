import Image from '@hi7/assets/icon/solution-9.svg';
import data from '@hi7/assets/lotties/solution-9.json';

export const PROXY = {
  cta: 'Register Now',
  title: {
    h1: 'Flash Sales',
    p: `When purchasing multiple pairs of limited-edition sneakers, use ElfProxy's dynamic residential proxies to bypass regional restrictions and significantly increase your success rate.`,
  },
  feature: [
    'Billions of High-quality IP Pool',
    'Exclusive Primary Operator Resources',
    'Extremely Cost-Effective',
  ],
  background: {
    left: 'bg-[#C9F9F7]',
    right: 'text-[#E3FFFB]',
    Image,
  },
  lottie: {
    // Masking: MaskProxy1, // TBC
    data,
    position: 'bottom-[-5dvw] right-[10dvw] lg:bottom-0 lg:right-[7dvw]',
  },
} as const;

export default PROXY;
