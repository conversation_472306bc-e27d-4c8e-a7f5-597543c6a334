'use client';

import Footer from '@hi7/components/Footer';
import Header from '@hi7/components/Header';
import type { i18n } from '@hi7/interface/i18n';
import HeaderProvider from '@hi7/provider/HeaderProvider';
import LocaleProvider from '@hi7/provider/LocaleProvider';
import { type ReactNode } from 'react';

type LayoutContainerProps = {
  children: ReactNode;
  pathname: string;
} & i18n;

const LayoutContent = ({ children, locale }: LayoutContainerProps) => {
  return (
    <LocaleProvider locale={locale}>
      <HeaderProvider>
        <main id="main">
          <Header />
          {children}
          <Footer />
        </main>
      </HeaderProvider>
    </LocaleProvider>
  );
};

export default LayoutContent;
