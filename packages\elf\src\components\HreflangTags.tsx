'use client';

import { type Locale } from '@hi7/lib/i18n';
import { usePathname } from 'next/navigation';

interface HreflangTagsProps {
  locale: Locale;
}

export default function HreflangTags({ locale }: HreflangTagsProps) {
  const pathname = usePathname();
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com';
  
  // Since the app uses cookie-based localization, we'll use query parameters for hreflang
  // This is a common approach when URLs don't contain locale information
  const currentUrl = `${baseUrl}${pathname}`;
  
  return (
    <>
      {/* Current page in English */}
      <link
        rel="alternate"
        hrefLang="en"
        href={`${currentUrl}?lang=en`}
      />
      
      {/* Current page in Chinese */}
      <link
        rel="alternate"
        hrefLang="zh-CN"
        href={`${currentUrl}?lang=zh`}
      />
      
      {/* Default/fallback for unspecified languages */}
      <link
        rel="alternate"
        hrefLang="x-default"
        href={currentUrl}
      />
      
      {/* Regional variations */}
      <link
        rel="alternate"
        hrefLang="en-US"
        href={`${currentUrl}?lang=en`}
      />
      
      <link
        rel="alternate"
        hrefLang="en-GB"
        href={`${currentUrl}?lang=en`}
      />
      
      <link
        rel="alternate"
        hrefLang="zh-CN"
        href={`${currentUrl}?lang=zh`}
      />
      
      <link
        rel="alternate"
        hrefLang="zh-TW"
        href={`${currentUrl}?lang=zh`}
      />
      
      <link
        rel="alternate"
        hrefLang="zh-HK"
        href={`${currentUrl}?lang=zh`}
      />
    </>
  );
}
