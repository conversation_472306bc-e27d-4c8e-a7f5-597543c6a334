'use client';

import newsImage1 from '@hi7/assets/background/news-detail-1.png';
import Clock from '@hi7/assets/icon/clock.svg';
import { useI18n } from '@hi7/lib/i18n';
import Image from 'next/image';
import { F_LATEST_NEWS, F_NEWS, F_NEWS_TAGS } from '../config';

function page() {
  const t = useI18n();
  return (
    <div className="flex items-center justify-center">
      <div className="w-full">
        <div className="relative pt-[32px] pb-[80px] text-[#002035] lg:pt-[78px] lg:pb-[120px]">
          <div className="relative px-4 lg:px-30">
            <h1 className="mb-[32px] text-[36px] leading-[1.2] font-semibold lg:text-[48px]">
              哪个海外代理IP平台最值得推荐：排名前十强测评
            </h1>

            <Image src={newsImage1} width={2400} height={800} alt="" />

            <div className="pt-[30px] pb-[68px]">
              <div className="mb-[10px] inline-flex rounded-[4px] bg-[#007D93] px-2 py-1 text-[14px] text-white">
                {t('Case Studies')}
              </div>
              <p className="mb-[28px] text-[14px] text-[#8C8C8C]">2025-04-01</p>

              <div className="flex flex-row gap-[74px]">
                <div className="flex flex-1 flex-col gap-4 py-2.5">
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    越来越多的人开始关注和使用海外代理IP服务。那么，哪个海外代理IP平台最值得推荐呢？本文将对排名前十的海外代理IP平台进行详细测评，帮助你找到最合适的选择。这里推荐使用Elfproxy海外代理IP
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    1. Elfproxy
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    Elfproxy作为全球知名的VPN服务提供商，以其稳定的连接和广泛的服务器覆盖受到用户的欢迎。Elfproxy在全球1亿+纯净IP，保证质量，覆盖60多个国家。它不仅提供优质的代理IP服务，还在隐私保护和数据加密方面表现出色。
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    2. ExpressVPN
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    ExpressVPN以其高速连接和强大的隐私保护功能而著称。该平台在94个国家拥有3000多台服务器，能够满足用户的各种需求。ExpressVPN的优势在于其用户友好的界面和24/7的客户支持服务，但成本较高也是其一大缺点。
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    3. CyberGhost
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    CyberGhost提供了大量的服务器选择，覆盖90多个国家，共有6400多台服务器。它以其简单易用的界面和合理的价格受到用户青睐。CyberGhost在隐私保护方面也做得不错，适合那些对预算有要求但又需要可靠服务的用户。
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    4. Surfshark
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    Surfshark是一个相对较新的VPN服务提供商，但其表现不可小觑。它在65个国家拥有3200多台服务器，价格相对较低，性价比高。Surfshark提供无限设备连接和强大的隐私保护功能，适合家庭用户和小型企业。
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    5. Private Internet Access (PIA)
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    PIA以其注重隐私和安全而闻名。该平台在30多个国家拥有3300多台服务器，价格适中。PIA的优势在于其开源的客户端和严格的无日志政策，适合那些对隐私有高要求的用户。
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    6. IPVanish
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    IPVanish提供了全球75个国家、1600多台服务器的覆盖。它以其高速连接和无限带宽受到用户的青睐。IPVanish的隐私保护也相当强大，但其用户界面相对复杂，不太适合技术小白。
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    7. Hotspot Shield
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    Hotspot
                    Shield是一个知名的VPN服务提供商，在80多个国家拥有3200多台服务器。它以其高速连接和免费服务模式吸引了大量用户。虽然免费版有一定限制，但其付费版提供了更强大的功能和更广泛的覆盖。
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    8. Windscribe
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    Windscribe提供了全球超过63个国家、110多个城市的服务器选择。它以其灵活的免费和付费计划受到用户的好评。Windscribe注重用户隐私，并提供广告拦截和防止追踪等功能，非常适合注重隐私保护的用户。
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    9. ProtonVPN
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    ProtonVPN由科学家和开发人员团队创建，专注于提供高度安全和隐私保护的服务。它在54个国家拥有1000多台服务器，免费版也充分展现了其可靠性。不过，ProtonVPN的服务器数量相对较少，可能会影响部分用户的选择。
                  </p>

                  <b className="text-[24px] leading-[1.2] font-semibold text-[#000]">
                    10. HideMyAss (HMA)
                  </b>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    HMA是一个历史悠久的VPN服务提供商，在190多个国家拥有1040多台服务器。它以其广泛的服务器覆盖和易用的界面受到用户喜爱。HMA适合那些需要大量区域选择的用户，当然，其价格也相对较高。
                  </p>

                  <p className="leading-[1.4] text-[#1E1E1E]">
                    选择适合的海外代理IP平台需要考虑多个因素，包括服务器覆盖范围、连接速度、隐私保护和价格等。本文推荐的十大平台均各有所长，可以根据自身需求进行选择。
                  </p>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    无论你是希望绕过地理限制，还是出于隐私保护的需求，以上任何一个平台都能为你提供可靠的服务。这里推荐使用Elfproxy海外代理IP
                  </p>
                  <p className="leading-[1.4] text-[#1E1E1E]">
                    Tag：Proxy云控, IP, VPN
                  </p>
                </div>
                <div className="hidden flex-[0.4] flex-col gap-[40px] lg:flex">
                  <div className="flex min-w-[320px] flex-col gap-4 rounded-[10px] border border-[#D9D9D9] p-5">
                    <h4 className="text-[20px] leading-[1.2] font-semibold">
                      {t('Latest Article')}
                    </h4>

                    {F_LATEST_NEWS.map((item, idx) => (
                      <p className="leading-[1.4]">{item}</p>
                    ))}
                  </div>
                  <div className="flex min-w-[320px] flex-col gap-4 rounded-[10px] border border-[#D9D9D9] p-5">
                    <h4 className="text-[20px] leading-[1.2] font-semibold">
                      {t('Tag')}
                    </h4>

                    <div className="flex flex-wrap gap-[12px]">
                      {F_NEWS_TAGS.map((item, idx) => (
                        <p className="rounded-full border border-[#1E1E1E]/45 px-[12px] py-[3px] leading-[1.4]">
                          {t(item)}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="mb-[30px] text-[24px] leading-[1.2] font-semibold">
                {t('Related Posts')}
              </h4>

              <div className="grid gap-x-[60px] gap-y-[40px] lg:mb-[70px] lg:grid-cols-3 lg:gap-y-[34px]">
                {F_NEWS.slice(0, 3).map((item, idx) => (
                  <div
                    key={`fake-${idx}`}
                    className="mb-2.5 flex flex-col items-start gap-2.5"
                  >
                    <Image
                      src={item.image}
                      alt=""
                      width={360}
                      height={240}
                      className="mb-4 h-[200px] w-full rounded-[12px] object-cover"
                    />
                    <div className="rounded-[4px] bg-[#007D93] px-2 py-1 text-[14px] text-white">
                      {t(item.category)}
                    </div>
                    <b className="text-[24px] leading-[1.2]">{item.title}</b>
                    <p className="leading-[1.4] text-[#1E1E1E]/65">
                      {item.desc}
                    </p>
                    <div className="flex w-full flex-row items-center pb-2.5 text-[14px] leading-[1.4] text-[#8C8C8C]">
                      <p className="flex-1">{item.createdAt}</p>
                      <p className="flex flex-row items-center gap-2 self-end">
                        <Clock />
                        {item.readTime}
                        {t(' min read')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default page;
