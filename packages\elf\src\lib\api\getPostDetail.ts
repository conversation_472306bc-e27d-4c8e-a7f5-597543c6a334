import type { WP_REST_API_Post } from 'wp-types';

export const getPostDetail = async (id: string) => {
  try {
    const response = await fetch(`${process.env.WP_API_URL}/posts/${id}`, {
      next: {
        revalidate: 5 * 60, // 5 minutes in seconds
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw data;
    }

    return data as Promise<WP_REST_API_Post>;
  } catch (error) {
    console.log(error);
    return {
      code: 'rest_no_route',
      message: 'No route found',
    };
  }
};
