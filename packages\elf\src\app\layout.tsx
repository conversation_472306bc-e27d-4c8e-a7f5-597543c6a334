import QueryClientProviders from '@hi7/provider/QueryClientRegistry';

import Fab from '@hi7/components/Fab';
import FloatContact from '@hi7/components/FloatContact';
import StructuredData from '@hi7/components/StructuredData';
import {
  generateHreflangAlternates,
  getAlternateLocale,
  getOpenGraphLocale,
} from '@hi7/helpers/hreflang';
import { getBaseurl, getLocale, getPathname } from '@hi7/helpers/pathname';
import { montserratFont as enFont, orbitronFont } from '@hi7/lib/font';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';
import ProgressProvider from '@hi7/provider/ProgressProvider';
import ZustandContext from '@hi7/provider/ZustandContext';
import clsx from 'clsx';
import { Noto_Sans_SC as Noto } from 'next/font/google';
import Head from 'next/head';
import Script from 'next/script';
import './globals.css';
import LayoutContent from './layout-content';

const zhFont = Noto({
  weight: ['400', '700'],
  subsets: ['latin'],
});

const FONTS = {
  en: enFont,
  zh: zhFont,
} satisfies Record<Locale, typeof enFont>;

export const generateMetadata = async () => {
  const locale = await getLocale();
  const baseUrl =
    process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com';

  return {
    metadataBase: new URL(baseUrl),
    title: t(locale)(
      'ElfProxy Proxy IP Service Provider - Static IP/Dynamic IP/Native IP - Global Pure Residential IP Network',
    ),
    description: t(locale)(
      'ElfProxy has a global network of pure residential IPs in over 200 countries - providing dynamic IP pools, dynamic and static IPs, native residential IPs, HTTP(s) proxies, Socks5 proxies, and more. IP quality is guaranteed, with triple compensation in case of any issues! High anonymity dedicated IPs meet the needs of data scraping, cross-border e-commerce, social media operation, and more.',
    ),
    keywords: t(locale)(
      'ElfProxy, overseas IP, dynamic IP, static IP, static residential proxy, static residential IP, native IP',
    ),
    alternates: {
      canonical: baseUrl,
      languages: generateHreflangAlternates(baseUrl),
    },
    robots: {
      index: true,
      follow: true,
    },
    viewport: {
      width: 'device-width',
      initialScale: 1,
      maximumScale: 1,
    },
    themeColor: '#070707',
    manifest: '/manifest.json',
    icons: {
      icon: [
        { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
        { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
        { url: '/icon.svg', type: 'image/svg+xml' },
      ],
      apple: [
        { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
      ],
    },
    // OpenGraph Meta Tags
    openGraph: {
      type: 'website',
      siteName: 'ElfProxy',
      title: t(locale)(
        'ElfProxy Proxy IP Service Provider - Static IP/Dynamic IP/Native IP - Global Pure Residential IP Network',
      ),
      description: t(locale)(
        'ElfProxy has a global network of pure residential IPs in over 200 countries - providing dynamic IP pools, dynamic and static IPs, native residential IPs, HTTP(s) proxies, Socks5 proxies, and more.',
      ),
      url: baseUrl,
      images: [
        {
          url: '/og-image.png',
          width: 1200,
          height: 630,
          alt: 'ElfProxy - Global Residential Proxy IP Provider',
        },
      ],
      locale: getOpenGraphLocale(locale),
      alternateLocale: getAlternateLocale(locale),
    },
    // Twitter Card Meta Tags
    twitter: {
      card: 'summary_large_image',
      site: '@ElfProxy',
      creator: '@ElfProxy',
      title: t(locale)(
        'ElfProxy Proxy IP Service Provider - Static IP/Dynamic IP/Native IP',
      ),
      description: t(locale)(
        'Global network of pure residential IPs in over 200 countries. High-speed dynamic/static IPs for web scraping, e-commerce, and data collection.',
      ),
      images: ['/twitter-image.png'],
    },
  };
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [locale, baseurl, pathname] = await Promise.all([
    getLocale(),
    getBaseurl(),
    getPathname(),
  ]);

  const mainFont = FONTS[locale] ?? FONTS.en;

  return (
    <html lang={locale} className={clsx([mainFont, orbitronFont.variable])}>
      <head>
        <meta charSet="utf-8" />
      </head>
      {/* Google tag (gtag.js) */}
      {process.env.HI7_GA_ALT_TAG && (
        <>
          <Script
            async
            src={`https://www.googletagmanager.com/gtag/js?id=${process.env.HI7_GA_ALT_TAG}`}
          />
          <Script
            id="gtag-init"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${process.env.HI7_GA_ALT_TAG}');
              `,
            }}
          />
        </>
      )}

      {/* Google Ads/Google Analytics  */}
      {process.env.HI7_GA_TAG && (
        <>
          <Script
            id="init-ga-tag"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', '${process.env.HI7_GA_TAG}');`,
            }}
          />
          <Script
            id="init-ga-page-view"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `gtag('event', 'conversion', {'send_to': '${process.env.HI7_GA_TAG}/${process.env.HI7_GA_PAGE_VIEW_CONVERSION_LABEL}'});`,
            }}
          />
        </>
      )}

      {/* Microsoft Clarity Analytics  */}
      {process.env.HI7_CLARITY_TAG && (
        <Script
          id="ms-clarity"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "${process.env.HI7_CLARITY_TAG}");
            `,
          }}
        />
      )}

      {/* Meta/Facebook Pixel  */}
      {process.env.HI7_FB_TAG && (
        <>
          <Head>
            <noscript>
              {/* eslint-disable-next-line @next/next/no-img-element, jsx-a11y/alt-text */}
              <img
                height="1"
                width="1"
                style={{ display: 'none' }}
                src={`https://www.facebook.com/tr?id=${process.env.HI7_FB_TAG}&ev=PageView&noscript=1`}
              />
            </noscript>
          </Head>
          <Script id="facebook-pixel" strategy="afterInteractive">
            {`
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', ${process.env.HI7_FB_TAG});
    fbq('track', 'PageView');
  `}
          </Script>
        </>
      )}

      {/* Structured Data */}
      <StructuredData locale={locale} pathname={pathname} />

      <body>
        <FloatContact />
        <QueryClientProviders>
          <ZustandContext config={{ baseurl }}>
            <ProgressProvider>
              <LayoutContent locale={locale} pathname={pathname}>
                {children}
                <Fab />
              </LayoutContent>
            </ProgressProvider>
          </ZustandContext>
        </QueryClientProviders>
      </body>
    </html>
  );
}
