'use server';

import { DEFAULT_LOCALE } from '@hi7/configs/dictionary';
import { SUPPORTED_LOCALES } from '@hi7/configs/pathname';
import { type Locale } from '@hi7/lib/i18n';
import { headers } from 'next/headers';

export async function getPathname(): Promise<string> {
  return headers().get('x-next-url') ?? '';
}

export async function getLocale(): Promise<Locale> {
  const locale = headers().get('x-locale');
  return SUPPORTED_LOCALES.includes(locale as string)
    ? (locale as Locale)
    : DEFAULT_LOCALE;
}

export async function getBaseurl(): Promise<string> {
  // Since we no longer use locale prefixes in URLs, baseurl is just empty
  return '';
}
