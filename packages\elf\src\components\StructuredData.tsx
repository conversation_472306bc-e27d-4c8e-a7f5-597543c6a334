'use client';

import { type Locale } from '@hi7/lib/i18n';
import Script from 'next/script';

interface StructuredDataProps {
  locale: Locale;
  pathname: string;
}

export default function StructuredData({ locale, pathname }: StructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com';
  const currentUrl = `${baseUrl}${pathname}`;

  // Organization Schema
  const organizationSchema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'ElfProxy',
    alternateName: 'Elf Proxy',
    url: baseUrl,
    logo: `${baseUrl}/logo.png`,
    description: locale === 'zh' 
      ? 'ElfProxy在200多个国家拥有全球纯住宅IP网络，提供动态IP池、动态和静态IP、原生住宅IP、HTTP(s)代理、Socks5代理等服务。'
      : 'ElfProxy has a global network of pure residential IPs in over 200 countries - providing dynamic IP pools, dynamic and static IPs, native residential IPs, HTTP(s) proxies, Socks5 proxies, and more.',
    foundingDate: '2020',
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: ['English', 'Chinese'],
    },
    sameAs: [
      'https://twitter.com/ElfProxy',
      'https://www.facebook.com/ElfProxy',
      'https://www.linkedin.com/company/elfproxy',
    ],
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'SG',
    },
  };

  // Website Schema
  const websiteSchema = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'ElfProxy',
    url: baseUrl,
    description: locale === 'zh'
      ? 'ElfProxy提供优质的住宅代理服务，在200多个国家拥有1亿多个IP。获得高速动态/静态住宅IP、数据中心代理和原生IP，用于网络抓取、电子商务和数据收集。'
      : 'ElfProxy offers premium residential proxy services with 100M+ IPs across 200+ countries. Get high-speed dynamic/static residential IPs, datacenter proxies, and native IPs for web scraping, e-commerce, and data collection.',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${baseUrl}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    inLanguage: locale === 'zh' ? 'zh-CN' : 'en-US',
  };

  // Service Schema for Proxy Services
  const serviceSchema = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: locale === 'zh' ? 'ElfProxy 代理IP服务' : 'ElfProxy Residential Proxy Service',
    description: locale === 'zh'
      ? '提供全球住宅代理IP服务，支持动态IP、静态IP、数据中心IP等多种代理类型，适用于网络爬虫、电商、社交媒体等场景。'
      : 'Global residential proxy IP services supporting dynamic IPs, static IPs, datacenter IPs and more. Perfect for web scraping, e-commerce, social media, and data collection.',
    provider: {
      '@type': 'Organization',
      name: 'ElfProxy',
      url: baseUrl,
    },
    serviceType: locale === 'zh' ? '代理IP服务' : 'Proxy IP Service',
    areaServed: 'Worldwide',
    availableChannel: {
      '@type': 'ServiceChannel',
      serviceUrl: baseUrl,
      availableLanguage: ['English', 'Chinese'],
    },
    category: 'Internet Service',
    offers: {
      '@type': 'Offer',
      availability: 'https://schema.org/InStock',
      priceRange: '$$$',
      priceCurrency: 'USD',
    },
  };

  // Breadcrumb Schema (for non-home pages)
  const breadcrumbSchema = pathname !== '/' ? {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: baseUrl,
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: pathname.split('/').pop()?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Page',
        item: currentUrl,
      },
    ],
  } : null;

  return (
    <>
      {/* Organization Schema */}
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />

      {/* Website Schema */}
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />

      {/* Service Schema */}
      <Script
        id="service-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema),
        }}
      />

      {/* Breadcrumb Schema (if not home page) */}
      {breadcrumbSchema && (
        <Script
          id="breadcrumb-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(breadcrumbSchema),
          }}
        />
      )}
    </>
  );
}
