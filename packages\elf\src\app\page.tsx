import Client from '@hi7/components/Home/Client';
import CompactitiveEdge from '@hi7/components/Home/CompactitiveEdge';
import Experience from '@hi7/components/Home/Experience';
import Landing from '@hi7/components/Home/Landing';
import TopTier from '@hi7/components/Home/TopTier';
import Why from '@hi7/components/Home/Why';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';
import { generateOpenGraphMeta } from '@hi7/lib/seo/generateOpenGraph';
import { getCanonicalURL } from '@hi7/lib/seo/getCanonical';

export const generateMetadata = async () => {
  const locale = await getLocale();
  const canonical = getCanonicalURL();

  const title = t(locale)(
    'ElfProxy - Global Residential Proxy IP Provider | High-Speed Dynamic & Static IPs',
  );
  const description = t(locale)(
    'ElfProxy offers premium residential proxy services with 100M+ IPs across 200+ countries. Get high-speed dynamic/static residential IPs, datacenter proxies, and native IPs for web scraping, e-commerce, and data collection with 99.9% uptime.',
  );

  const openGraphMeta = generateOpenGraphMeta({
    title,
    description,
    url: canonical,
    images: ['/logo.svg'],
    siteName: 'ElfProxy',
    locale: locale === 'zh' ? 'zh_CN' : 'en_US',
    type: 'website',
  });

  return {
    title,
    description,
    keywords: t(locale)(
      'residential proxy, proxy IP, dynamic IP, static IP, datacenter proxy, web scraping proxy, global IP pool, high-speed proxy',
    ),
    alternates: {
      canonical,
    },
    robots: {
      index: true,
      follow: true,
    },
    ...openGraphMeta,
  };
};

export default async function Page() {
  return (
    <>
      <Landing />
      <CompactitiveEdge />
      <Why />
      <TopTier />
      <Client />
      <Experience />
    </>
  );
}
