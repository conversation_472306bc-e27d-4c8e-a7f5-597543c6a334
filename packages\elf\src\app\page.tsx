import Client from '@hi7/components/Home/Client';
import CompactitiveEdge from '@hi7/components/Home/CompactitiveEdge';
import Experience from '@hi7/components/Home/Experience';
import Landing from '@hi7/components/Home/Landing';
import TopTier from '@hi7/components/Home/TopTier';
import Why from '@hi7/components/Home/Why';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();

  return {
    title: t(locale)(
      'ElfProxy - Global Residential Proxy IP Provider | High-Speed Dynamic & Static IPs',
    ),
    description: t(locale)(
      'ElfProxy offers premium residential proxy services with 100M+ IPs across 200+ countries. Get high-speed dynamic/static residential IPs, datacenter proxies, and native IPs for web scraping, e-commerce, and data collection with 99.9% uptime.',
    ),
    keywords: t(locale)(
      'residential proxy, proxy IP, dynamic IP, static IP, datacenter proxy, web scraping proxy, global IP pool, high-speed proxy',
    ),
    alternates: {
      canonical: process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com',
    },
    robots: {
      index: true,
      follow: true,
    },
  };
};

export default async function Page() {
  return (
    <>
      <Landing />
      <CompactitiveEdge />
      <Why />
      <TopTier />
      <Client />
      <Experience />
    </>
  );
}
