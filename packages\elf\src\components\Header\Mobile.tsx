'use client';

import clsx from 'clsx';
import { useState } from 'react';

import CloseButton from '@hi7/assets/icon/close-button.svg';
import Menu from '@hi7/assets/icon/menu.svg';
import Logo2 from '@hi7/assets/logo/logo-2.svg';
import Logo from '@hi7/assets/logo/logo.svg';
import Link from '@hi7/components/Link';
import MenuLink from '@hi7/components/MenuLink/Mobile';

import { useI18n } from '@hi7/lib/i18n';
import LinkButton from '../Form/LinkButton';
import LocaleSwitcher from '../LocaleSwitcher/Mobile';
import { ROUTES } from './config';

const HeaderMobile = () => {
  const [open, setOpen] = useState(false);

  const toggleMenu = () => setOpen((prev) => !prev);
  const t = useI18n();

  const closePanel = () => {
    setOpen(false);
  };

  const buttonChild = ROUTES.find((s) => s.asButton);

  return (
    <>
      <div className="h-14"></div>
      <nav
        className={clsx(
          'm-h-14 fixed top-0 z-30 max-h-full w-full transform overflow-auto px-4 py-2.5 transition-all duration-200',
          open ? 'bg-white text-[#1E1E1E]' : 'bg-[#002035] text-white',
        )}
      >
        <div className={clsx('flex items-center gap-4')}>
          <Link url={''} onClick={closePanel}>
            {open ? (
              <Logo2 width={86} height={36} />
            ) : (
              <Logo width={86} height={36} />
            )}
          </Link>
          <div className="flex-1" />

          {buttonChild && (
            <LinkButton
              target="_blank"
              type="main"
              size="S"
              url={buttonChild.url}
              onClick={closePanel}
            >
              {t(buttonChild.children as string)}
            </LinkButton>
          )}

          {!open && <Menu onClick={toggleMenu} />}

          {open && <CloseButton onClick={closePanel} />}
        </div>

        {open && (
          <div className="flex flex-col px-2 pt-3 pb-7">
            <LocaleSwitcher />
            {ROUTES.filter((s) => !s.asButton).map((route, index) => (
              <MenuLink
                key={index}
                {...route}
                onClick={() => {
                  toggleMenu();
                  // route.onClick?.();
                }}
              />
            ))}
          </div>
        )}
      </nav>
    </>
  );
};

export default HeaderMobile;
