/**
 * Generate canonical URL
 * @param segments URL segments to append to base URL
 * @returns Complete canonical URL
 */
export function getCanonicalURL(segments: string[] = []): string {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com';
  
  // Filter out empty segments and join with '/'
  const path = segments.filter(Boolean).join('/');
  
  return path ? `${baseUrl}/${path}` : baseUrl;
}
