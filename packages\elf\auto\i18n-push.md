# Syncing Translations to Google Sheets

This project includes a script to push your translation data (from `zh.json`) to a Google Sheet for collaborative editing and review.

## Features

- Automatically creates or updates a Google Sheet named **Elfproxy Translation**.
- Each Git branch gets its own tab in the sheet.
- Shares the sheet with the `hiseven.com` domain and specified external emails.
- Formats cells for readability (column widths, highlights, italics).
- Adds a sync timestamp for tracking.

## Prerequisites

- Node.js installed.
- Google Cloud service account with access to Google Drive and Sheets APIs.
- `.env` file with the following variables:

    ```
    GOOGLE_PROJECT_ID=
    GOOGLE_PRIVATE_KEY_ID=
    GOOGLE_PRIVATE_KEY=
    GOOGLE_CLIENT_EMAIL=
    GOOGLE_CLIENT_ID=
    ```

## Usage

1. Place your translation file at `../src/dictionaries/zh.json` relative to the script.
2. Run the script:

     ```bash
    pnpm run elf:i18n:push
     ```

3. On first run, a new Google Sheet will be created and shared with the HiSeven team and specified external users.
4. Each branch will have its own tab for translations.

## Customization

- To add more external collaborators, update the `EXTERNAL_EMAILS` array in the script.
- Adjust column widths by modifying the `COLUMN_WIDTHS` array.

## Troubleshooting

- Ensure all required environment variables are set in your `.env` file.
- Make sure your Google Cloud service account has the necessary permissions.
- If you encounter permission errors, check your Google API credentials and sharing settings.

## Notes

- Special formatting is applied for certain keys (e.g., rows starting with `__SKIP_en_page` are highlighted).
- The script logs progress and errors to the console.

---

**Happy translating!**
