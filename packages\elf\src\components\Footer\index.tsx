'use client';

import FooterTg from '@hi7/assets/icon/footer-tg.svg';
import FooterWs from '@hi7/assets/icon/footer-ws.svg';
import Logo from '@hi7/assets/logo/logo.svg';
import { useGaConversion } from '@hi7/helpers/google-ads';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import Link from '../Link';
import { PROXIES, QUICK_ACCESS, SOLUTIONS } from './config';

const Footer = () => {
  const invokeTGConversion = useGaConversion({
    conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_TG_CONVERSION_LABEL,
  });
  const invokeWAConversion = useGaConversion({
    conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_WA_CONVERSION_LABEL,
  });
  const t = useI18n();
  const pathname = usePathname();

  const isHomePage = pathname === '' || pathname === '/';
  const isPricingPage = pathname === '/pricing';

  return (
    <footer
      className={clsx(
        'relative z-20 flex transform flex-col gap-[30px] bg-[#002035] px-4 py-15 text-white transition-all duration-200 lg:px-30 lg:py-4',
        isPricingPage && 'lg:mt-0',
        isHomePage && 'lg:mt-[-25dvh]',
      )}
    >
      <div className="flex w-full flex-col gap-[40px] lg:flex-row lg:gap-[60px]">
        <div className="flex flex-2 flex-col items-start justify-start gap-[20px] lg:gap-5">
          <Link url={''}>
            <Logo width={86} height={38} />
          </Link>
          <p>
            {t(
              'We are the top IP proxy experts specialising in social media operations, offering high-quality IPs covering the globe. This includes dynamic residential IPs, static data centre IPs, static residential IPs and native residential IPs.',
            )}
          </p>
          <div className="hidden flex-col gap-2.5 lg:flex">
            <p className="text-lg text-[#31F4A0]">{t('Contact Us')}</p>
            <a
              href="https://007tg.com/ccs/elfproxy"
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => invokeTGConversion()}
            >
              <div className="flex gap-2 text-[14px]">
                <FooterTg className="rounded-full" />
                {t('Customer Service')}
              </div>
              <p className="text-[12px] leading-[1.4] text-[#FFC525] underline">
                {t(
                  'Get 10 residential IPs + 200MB dynamic traffic immediately',
                )}
              </p>
            </a>
            <a
              href="https://007tg.com/ccs/elfproxy_wa"
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => invokeWAConversion()}
            >
              <div className="flex gap-2 text-[14px]">
                <FooterWs />
                {t('Customer Service')}
              </div>
              <p className="text-[12px] leading-[1.4] text-[#FFC525] underline">
                {t(
                  'Get 10 residential IPs + 200MB dynamic traffic immediately',
                )}
              </p>
            </a>
          </div>
        </div>

        <div className="hidden flex-[1.5] lg:block"></div>

        <div className="flex flex-[0.8] flex-col gap-2.5 whitespace-nowrap">
          <p className="text-lg text-[#31F4A0]">{t('Quick Access')}</p>

          {QUICK_ACCESS.map((item) => (
            <Link key={`qs-${item.url}`} url={item.url}>
              {t(item.children as string)}
            </Link>
          ))}
        </div>

        <div className="flex flex-1 flex-col gap-2.5 whitespace-nowrap">
          <p className="text-lg text-[#31F4A0]">{t('Proxies')}</p>

          {PROXIES.map((item) => (
            <Link key={`py-${item.url}`} url={item.url} className={item.order}>
              {t(item.children as string)}
            </Link>
          ))}
        </div>

        <div className="flex w-[200px] flex-col gap-2.5 whitespace-nowrap">
          <p className="text-lg text-[#31F4A0]">{t('Solutions')}</p>

          {SOLUTIONS.map((item) => (
            <Link key={`sl-${item.url}`} url={item.url} className={item.order}>
              {t(item.children as string)}
            </Link>
          ))}
        </div>

        <div className="flex flex-col gap-2.5 lg:hidden">
          <p className="text-lg text-[#31F4A0]">{t('Contact Us')}</p>
          <a
            href="https://007tg.com/ccs/elfproxy"
            target="_blank"
            rel="noopener noreferrer"
          >
            <div className="flex gap-2 text-[14px]">
              <FooterTg className="rounded-full" />
              {t('Customer Service')}
            </div>
            <p className="text-[12px] leading-[1.4] text-[#FFC525] underline">
              {t('Get 10 residential IPs + 200MB dynamic traffic immediately')}
            </p>
          </a>
          <a
            href="https://007tg.com/ccs/elfproxy_wa"
            target="_blank"
            rel="noopener noreferrer"
          >
            <div className="flex gap-2 text-[14px]">
              <FooterWs />
              {t('Customer Service')}
            </div>
            <p className="text-[12px] leading-[1.4] text-[#FFC525] underline">
              {t('Get 10 residential IPs + 200MB dynamic traffic immediately')}
            </p>
          </a>
        </div>
      </div>

      <div className="flex flex-col gap-[24px] text-[14px] leading-[1.4] lg:flex-row lg:gap-0">
        <p className="flex-1">
          © {new Date().getFullYear()} ElfProxy. {t('All Rights Reserved')}
        </p>

        <div className="flex gap-[24px]">
          <a href="">{t('Privacy')}</a>
          <a href="">{t('Terms of use')}</a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
