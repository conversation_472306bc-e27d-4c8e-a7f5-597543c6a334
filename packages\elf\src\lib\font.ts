import { Montserrat } from 'next/font/google';
import localFont from 'next/font/local';

export const montserratFont = Montserrat({
  weight: ['400', '600', '700'],
  subsets: ['latin'],
});

export const orbitronFont = localFont({
  src: [
    {
      path: '../app/fonts/Orbitron-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../app/fonts/Orbitron-SemiBold.woff2',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../app/fonts/Orbitron-Bold.woff2',
      weight: '700',
      style: 'normal',
    },
  ],
  variable: '--font-orbitron',
  preload: true,
  fallback: ['sans-serif'],
  display: 'swap',
  style: 'normal',
});
