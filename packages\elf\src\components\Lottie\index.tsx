'use client';

import dynamic from 'next/dynamic';

import { useSearchParams } from 'next/navigation';
import type React from 'react';
import { useState } from 'react';
import type { LottieProps as LibLottieProps } from 'react-lottie-player';

type LottieProps = LibLottieProps & {
  playOnHover?: boolean;
  masking?: React.ReactNode;
};

const LibLottie = dynamic(() => import('react-lottie-player'), {
  ssr: false,
});

const defaultConfigs: LottieProps = {
  loop: true,
  play: true,
};

const Lottie = (props: LottieProps) => {
  const { masking, playOnHover, ...rest } = props;
  const [hoverPlaying, setHoverPlaying] = useState(false);
  const [, setIsStarted] = useState(false);

  const lottieProps = { ...defaultConfigs, ...rest };
  const searchParams = useSearchParams();
  const noAnimation = searchParams.has('no-animation');

  const handleHoverPlaying = (state: boolean) => {
    if (!playOnHover) return;

    setHoverPlaying(state);
  };

  if (noAnimation) {
    return masking;
  }

  return (
    <>
      <LibLottie
        {...lottieProps}
        loop={lottieProps.loop}
        onEnterFrame={() => setIsStarted(true)}
        play={playOnHover ? hoverPlaying : lottieProps.play}
        onMouseOver={() => handleHoverPlaying(true)}
        onMouseLeave={() => handleHoverPlaying(false)}
      />
      {/* {!isStarted && masking} */}
    </>
  );
};

export default Lottie;
