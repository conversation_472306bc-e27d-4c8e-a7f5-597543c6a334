'use client';

import SolutionBgLeft from '@hi7/assets/background/solution-bg-left.svg';
import SolutionBgRight from '@hi7/assets/background/solution-bg-right.svg';

import data1 from '@hi7/assets/lotties/solution-tmp-1.json';
import data2 from '@hi7/assets/lotties/solution-tmp-2.json';

import SolutionBgSmall from '@hi7/assets/background/solution-bg-sm.svg';
import SolutionBg from '@hi7/assets/background/solution-bg.svg';
import Check from '@hi7/assets/icon/check.svg';
import Lottie from '@hi7/components/Lottie';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import LinkButton from '../Form/LinkButton';
import { SOLUTION } from './config';

type Template1Props = {
  id:
    | 'solution-1'
    | 'solution-2'
    | 'solution-3'
    | 'solution-4'
    | 'solution-5'
    | 'solution-6'
    | 'solution-7'
    | 'solution-8'
    | 'solution-9'
    | 'solution-10';
};

function SolutionTemplate({ id }: Template1Props) {
  const t = useI18n();
  const locale = useLocale();
  const { cta, background, lottie, title, feature } =
    SOLUTION[id as keyof typeof SOLUTION];

  return (
    <>
      <div className="flex items-center justify-center overflow-hidden">
        <div className="w-full">
          <div
            className={clsx(
              'relative min-h-(--min-sm-h-hvh) overflow-hidden rounded-br-[40px] rounded-bl-[40px] px-4 pt-2.5 text-[#002035] lg:min-h-(--min-h-hvh) lg:rounded-br-[100px] lg:rounded-bl-[100px] lg:px-30 lg:pt-[40px]',
              background.left,
            )}
          >
            <SolutionBg
              className={clsx(
                'absolute bottom-0 hidden w-dvw lg:top-0 lg:left-0 lg:block lg:h-dvh',
                background.right,
              )}
            />
            <SolutionBgSmall
              className={clsx(
                'absolute top-[-100px] right-0 block w-dvw lg:hidden lg:h-dvh',
                background.right,
              )}
            />

            <div className="relative flex flex-col items-center justify-center gap-[30px] lg:grid lg:min-h-(--min-h-hvh) lg:grid-cols-[1fr_0.75fr] lg:gap-[15dvw]">
              <div className="flex flex-col items-start gap-[24px] lg:gap-[30px]">
                <h1 className="text-[36px] leading-none text-[#002035] lg:text-[64px]">
                  {t(title.h1)}
                </h1>
                <p className="leading-[1.4] lg:text-[18px]">{t(title.p)}</p>
                <LinkButton
                  type="main"
                  size="L"
                  className="flex"
                  url={
                    locale === 'en'
                      ? 'https://007tg.com/ccs/elfproxy'
                      : 'https://007tg.com/ccs/elfproxy'
                  }
                  target="_blank"
                >
                  {t(cta)}
                </LinkButton>
                <div className="flex flex-col gap-4 text-[16px] leading-[1.2] lg:flex-row lg:flex-wrap">
                  {feature.map((s) => (
                    <div className="flex gap-2.5">
                      <Check className="w-[18px] text-[#27CA40]" />
                      {t(s)}
                    </div>
                  ))}
                </div>
              </div>
              <div className="relative">
                <background.Image className="w-[60dvw] lg:w-[33dvw]" />

                <Lottie
                  className={clsx(
                    'absolute h-[35dvw] w-[35dvw] lg:h-[13dvw] lg:w-[13dvw]',
                    lottie.position,
                  )}
                  animationData={lottie.data}
                  // masking={<lottie.Masking className="w-[55dvw] lg:w-[22dvw]" />}
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-[100px] px-4 pt-[66px] pb-[105px] lg:block lg:px-30 lg:pt-[72px] lg:pb-[200px]">
            <div className="grid items-center justify-center gap-[40px] lg:grid-cols-[0.75fr_1fr] lg:gap-[180px]">
              <div className="relative order-2 lg:order-1">
                <SolutionBgLeft className="w-full lg:w-[30dvw]" />
                <Lottie
                  className="absolute bottom-[-7dvw] left-[23dvw] h-[35dvw] w-[35dvw] lg:bottom-[-2dvw] lg:left-[8dvw] lg:h-[11dvw] lg:w-[11dvw]"
                  animationData={data1}
                  // masking={<lottie.Masking className="w-[55dvw] lg:w-[22dvw]" />}
                />
              </div>

              <div className="order-1 flex flex-col gap-4 lg:order-2">
                <h2 className="text-[32px] leading-none text-[#002035] lg:text-[48px]">
                  {t('Why Choose ElfProxy?')}
                </h2>
                <p className="text-[16px] leading-[1.4] text-[#656578] lg:text-[18px]">
                  {t(
                    'ElfProxy partners with top global operators to deliver high-quality IPs across 200+ countries. Access premium proxies with options for specific locations, all backed by high-speed connections and a 99.99% uptime guarantee, ensuring seamless business operations!',
                  )}
                </p>
              </div>
            </div>
            <div className="grid items-center justify-center gap-[40px] lg:grid-cols-[1fr_0.75fr] lg:gap-[180px]">
              <div className="flex flex-col gap-4">
                <h2 className="text-[32px] leading-none text-[#002035] lg:text-[48px]">
                  {t(
                    `Choose ElfProxy's Top Proxy for Your Social Media Matrix`,
                  )}
                </h2>
                <p className="text-[16px] leading-[1.4] text-[#656578] lg:text-[18px]">
                  {t(
                    'Our premium IP proxies consist of genuine IP addresses from real users, ensuring you remain undetected and unblocked. ElfProxy offers customised solutions tailored to diverse user needs, effectively meeting various business requirements.',
                  )}
                </p>
                <LinkButton
                  type="main"
                  size="L"
                  className="self-start"
                  url={
                    locale === 'en'
                      ? 'https://007tg.com/ccs/elfproxy'
                      : 'https://007tg.com/ccs/elfproxy'
                  }
                  target="_blank"
                >
                  {t(cta)}
                </LinkButton>
              </div>

              <div className="relative">
                <SolutionBgRight className="w-full lg:w-[30dvw]" />

                <Lottie
                  className="absolute bottom-[-7dvw] left-[18dvw] h-[35dvw] w-[35dvw] lg:bottom-[-2dvw] lg:left-[8dvw] lg:h-[11dvw] lg:w-[11dvw]"
                  animationData={data2}
                  // masking={<lottie.Masking className="w-[55dvw] lg:w-[22dvw]" />}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default SolutionTemplate;
