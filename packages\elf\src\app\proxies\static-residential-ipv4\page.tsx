import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Static Residential IP Proxy - ISP Proxy/Broadband IP - ElfProxy Pure Residential IP',
    ),
    description: t(locale)(
      'Global pure residential proxy IPs with free trial, just like using a local real home network, high anonymity dedicated IP, ensuring your business/account is more stable and secure. Provides static residential IP, static ISP proxy, dedicated IP, fixed IP, supports HTTP(s) proxy and Socks5 proxy protocols.',
    ),
    keywords: t(locale)(
      'Static IP, Residential IP, Static Residential, Static Residential Proxy, Static Residential IP, Static Residential IP Trial, Static IP Purchase',
    ),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com'}/proxies/static-residential-ipv4`,
    },
  };
};

function Proxy() {
  return <ProxyTemplate id="proxy-4" />;
}

export default Proxy;
