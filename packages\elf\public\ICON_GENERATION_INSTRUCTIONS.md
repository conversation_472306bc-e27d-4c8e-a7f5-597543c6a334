# Icon Generation Instructions

To complete the SEO enhancement, you need to generate the following icon files from the existing `packages/elf/src/app/icon.svg`:

## Required Icon Files

### 1. Favicon Files
- `favicon.ico` (16x16, 32x32, 48x48 pixels) - Replace the placeholder
- `favicon-16x16.png` (16x16 pixels)
- `favicon-32x32.png` (32x32 pixels)

### 2. Apple Touch Icons
- `apple-touch-icon.png` (180x180 pixels)

### 3. Android Chrome Icons
- `android-chrome-192x192.png` (192x192 pixels)
- `android-chrome-512x512.png` (512x512 pixels)

### 4. Social Media Images
- `og-image.png` (1200x630 pixels) - OpenGraph image for general pages
- `og-image-home.png` (1200x630 pixels) - OpenGraph image for home page
- `twitter-image.png` (1200x630 pixels) - Twitter Card image for general pages
- `twitter-image-home.png` (1200x630 pixels) - Twitter Card image for home page
- `logo.png` (400x400 pixels) - Company logo for structured data

## Generation Steps

1. **Use the source SVG**: `packages/elf/src/app/icon.svg`
2. **Online tools you can use**:
   - [Favicon.io](https://favicon.io/favicon-converter/)
   - [RealFaviconGenerator](https://realfavicongenerator.net/)
   - [Canva](https://www.canva.com/) for social media images

3. **For social media images**:
   - Create branded images with the ElfProxy logo
   - Include key messaging about proxy services
   - Use the brand colors: Primary #070707, Accent #31F4A0

4. **Replace placeholder files** in the `/public` directory

## File Locations
All files should be placed in: `packages/elf/public/`

## Verification
After generating the files, test the implementation by:
1. Running the development server
2. Checking browser dev tools for proper favicon loading
3. Using social media debuggers:
   - [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
   - [Twitter Card Validator](https://cards-dev.twitter.com/validator)
   - [LinkedIn Post Inspector](https://www.linkedin.com/post-inspector/)

## Notes
- The current implementation references these files in the metadata
- The manifest.json is already configured to use these icons
- All meta tags are properly set up in the layout files
