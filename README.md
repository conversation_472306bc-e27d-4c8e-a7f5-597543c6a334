# Project Setup

## Install Dependencies

```bash
pnpm i
```

## Start Development Server

```bash
pnpm dev # run all development tasks
# or
pnpm elf:dev
```

## Install package in project/workspace

```bash
# Change to the path you want
cd packages/elf

# Add/install with flag '-w' to install in workspace

pnpm install [packages] -w
```

## Update Translation

To update translation files from Google Sheets, use the following commands:

### Pull latest translations

```bash
pnpm elf:i18n:pull
```
This command fetches the latest translations from Google Sheets and updates the local JSON files.

### Push local changes to Google Sheets

```bash
pnpm elf:i18n:push
```
Use this command to upload your local translation changes back to Google Sheets.

Refer to [`auto/i18n-pull.md`](packages/elf/auto/i18n-pull.md) and [`auto/i18n-push.md`](packages/elf/auto/i18n-push.md) for more details.


### To run local host

### Open Integrated Terminal (Right click on the folder)

Path: packages/elf

Run: pnpm ./auto/dev dev

Thanks! 
