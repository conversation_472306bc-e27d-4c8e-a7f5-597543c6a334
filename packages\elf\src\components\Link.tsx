'use client';

import { useBaseurl } from '@hi7/helpers/pathname/client';
import type { LinkProps as LibLinkProps } from 'next/link';
import LibLink from 'next/link';

export type LinkProps = Omit<
  React.AnchorHTMLAttributes<HTMLAnchorElement>,
  keyof LibLinkProps
> &
  OmitStrict<LibLinkProps, 'href'> & {
    children?: React.ReactNode;
  } & React.RefAttributes<HTMLAnchorElement> & {
    url: string;
  };

const Link = (props: LinkProps) => {
  const { url: rawUrl, ...rest } = props;

  const baseurl = useBaseurl();
  const includeBaseurl = !rawUrl.startsWith('http');

  const cleanedUrl = rawUrl.startsWith('/') ? rawUrl : `/${rawUrl}`;
  const safeBaseurl = baseurl === '/' ? '' : baseurl;

  const url = includeBaseurl ? `${safeBaseurl}${cleanedUrl}` : rawUrl;

  return <LibLink {...rest} href={url} />;
};

export default Link;
