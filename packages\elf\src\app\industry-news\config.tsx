import newsImage2_1 from '@hi7/assets/background/news-2-1.png';
import newsImage2_2 from '@hi7/assets/background/news-2-2.png';
import newsImage2_3 from '@hi7/assets/background/news-2-3.png';
import newsImage2_4 from '@hi7/assets/background/news-2-4.png';
import newsImage2_5 from '@hi7/assets/background/news-2-5.png';
import newsImage2_6 from '@hi7/assets/background/news-2-6.png';
import newsImage2_7 from '@hi7/assets/background/news-2-7.png';
import newsImage2_8 from '@hi7/assets/background/news-2-8.png';
import newsImage2_9 from '@hi7/assets/background/news-2-9.png';

export const F_NEWS = [
  {
    image: newsImage2_1,
    category: 'Marketing News',
    title: '如何通过指纹浏览器隐藏真实IP？5个高效匿名上网技巧',
    desc: '隐藏真实IP地址已成为保护隐私、避免账号关联或访问地域限制内容的关键需求。而指纹浏览器+IP代',
    createdAt: '2025-04-01',
    readTime: '6',
  },
  {
    image: newsImage2_2,
    category: 'Marketing News',
    title: '住宅IP vs 数据中心代理：哪种更适合Pinterest账号矩阵？',
    desc: '在Pinterest账号矩阵运营中，IP代理的选择直接影响账号安全、内容分发效率和抗封锁能力。住宅IP',
    createdAt: '2025-04-01',
    readTime: '6',
  },
  {
    image: newsImage2_3,
    category: 'Marketing News',
    title: 'TikTok低播/0播？3 步用原生IP秒过审、直跳10万流量池！',
    desc: '在 TikTok 内容竞争白热化的当下，“低播0播”“账号关联被限流”“审核频繁不通过”成为创作者和矩阵运',
    createdAt: '2025-04-01',
    readTime: '6',
  },
  {
    image: newsImage2_4,
    category: 'Marketing News',
    title: '如何通过指纹浏览器隐藏真实IP？5个高效匿名上网技巧',
    desc: '隐藏真实IP地址已成为保护隐私、避免账号关联或访问地域限制内容的关键需求。而指纹浏览器+IP代',
    createdAt: '2025-04-01',
    readTime: '6',
  },
  {
    image: newsImage2_5,
    category: 'Marketing News',
    title: '住宅IP vs 数据中心代理：哪种更适合Pinterest账号矩阵？',
    desc: '在Pinterest账号矩阵运营中，IP代理的选择直接影响账号安全、内容分发效率和抗封锁能力。住宅IP',
    createdAt: '2025-04-01',
    readTime: '6',
  },
  {
    image: newsImage2_6,
    category: 'Marketing News',
    title: 'TikTok低播/0播？3 步用原生IP秒过审、直跳10万流量池！',
    desc: '在 TikTok 内容竞争白热化的当下，“低播0播”“账号关联被限流”“审核频繁不通过”成为创作者和矩阵运',
    createdAt: '2025-04-01',
    readTime: '6',
  },
  {
    image: newsImage2_7,
    category: 'Marketing News',
    title: '如何通过指纹浏览器隐藏真实IP？5个高效匿名上网技巧',
    desc: '隐藏真实IP地址已成为保护隐私、避免账号关联或访问地域限制内容的关键需求。而指纹浏览器+IP代',
    createdAt: '2025-04-01',
    readTime: '6',
  },
  {
    image: newsImage2_8,
    category: 'Marketing News',
    title: '住宅IP vs 数据中心代理：哪种更适合Pinterest账号矩阵？',
    desc: '在Pinterest账号矩阵运营中，IP代理的选择直接影响账号安全、内容分发效率和抗封锁能力。住宅IP',
    createdAt: '2025-04-01',
    readTime: '6',
  },
  {
    image: newsImage2_9,
    category: 'Marketing News',
    title: 'TikTok低播/0播？3 步用原生IP秒过审、直跳10万流量池！',
    desc: '在 TikTok 内容竞争白热化的当下，“低播0播”“账号关联被限流”“审核频繁不通过”成为创作者和矩阵运',
    createdAt: '2025-04-01',
    readTime: '6',
  },
];

export const F_LATEST_NEWS = [
  '什么是WhatsApp协议号及其2024年最新使用指南',
  '什么是WhatsApp协议号及其2024年最新使用指南',
  '什么是WhatsApp协议号及其2024年最新使用指南',
  '什么是WhatsApp协议号及其2024年最新使用指南',
  '什么是WhatsApp协议号及其2024年最新使用指南',
];

export const F_NEWS_TAGS = [
  'IP resources',
  'IPv4',
  'IPV6',
  'Dynamic residential IP',
  'IP proxy',
  'IP purchase',
  'Static data centre IP',
  'Dynamic data centre IP',
  'IP stability',
  'Global IP',
  'Native IP',
  'Residential IP',
  'Dynamic IP',
  'Static IP',
  'Proxy IP',
  'HTTP IP',
];

export const F_CATEGORIES = [
  'All',
  'Case Studies',
  'How To',
  'Industry News',
  'Product News',
  'Proxy Fundamentals',
  'Proxy Technical',
];
