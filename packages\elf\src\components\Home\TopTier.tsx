'use client';

import ttBg from '@hi7/assets/background/home-tt-1.png';
import TT6 from '@hi7/assets/icon/tt-6.svg';
import TT7 from '@hi7/assets/icon/tt-7.svg';
import { useGaConversion } from '@hi7/helpers/google-ads';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import Image from 'next/image';
import LinkButton from '../Form/LinkButton';
import { TT_DATA_1, TT_DATA_2 } from './config';

function TopTier() {
  const invokeTGConversion = useGaConversion({
    conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_TG_CONVERSION_LABEL,
  });
  const invokeWAConversion = useGaConversion({
    conversionLabel: process.env.NEXT_PUBLIC_HI7_GA_WA_CONVERSION_LABEL,
  });

  const { isMobile } = useScreenSize();
  const t = useI18n();

  const redirectUrl = 'https://007tg.com/ccs/elfproxy';

  const handleConversionClick = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    e.preventDefault();
    invokeTGConversion();
    invokeWAConversion();

    setTimeout(() => {
      window.open(redirectUrl, '_blank');
    }, 300);
  };

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative">
          <div className="relative flex flex-col items-center justify-center px-5 py-11 text-[#002035] lg:pt-[72px]">
            <div className="lg:w-[85dvw] lg:min-w-[1200px]">
              <h2 className="mb-[46px] text-center text-[36px] leading-none lg:text-[48px]">
                {t('Unparalleled ')}
                <span className="font-orbitron font-semibold text-[#31F4A0]">
                  {t('Top-Tier')}
                </span>
                {t(' Proxy Plans')}
              </h2>

              <div className="mb-[30px] grid grid-cols-1 gap-[30px] lg:auto-rows-[450px] lg:grid-cols-2">
                {TT_DATA_1.map((item, idx) => (
                  <div
                    key={`ttd-${idx}`}
                    className={clsx(
                      'group flex gap-4 rounded-[20px] p-6',
                      item.type === 'primary'
                        ? 'bg-[#007D93] text-white'
                        : 'bg-[#E3FFFB] text-[#002035]',
                    )}
                  >
                    <div className="flex flex-col justify-center gap-[14px]">
                      <item.Icon className="w-[56px] transition-all duration-500 group-hover:w-[56px] lg:w-[100px]" />

                      <b
                        className={clsx(
                          'text-[24px] font-semibold',
                          item.type === 'primary'
                            ? 'text-white'
                            : 'text-[#004A61]',
                        )}
                      >
                        {t(item.title)}
                      </b>
                      <p className="text-[16px]">{t(item.desc)}</p>
                      <ul className="h-[120px] list-disc overflow-hidden pl-6 text-[16px] transition-all duration-500 group-hover:h-[120px] lg:h-0">
                        {item.desc2.map((desc, descIdx) => (
                          <li key={`ttd2-${descIdx}`}>{t(desc)}</li>
                        ))}
                      </ul>
                      <div className="flex">
                        <LinkButton
                          className="hidden group-hover:hidden lg:flex"
                          type="text"
                          size="M"
                          url={redirectUrl}
                          target="_blank"
                        >
                          {t('Learn More')}
                        </LinkButton>
                        <LinkButton
                          className="flex group-hover:flex lg:hidden"
                          type="main"
                          size="M"
                          url={redirectUrl}
                          target="_blank"
                          onClick={handleConversionClick}
                        >
                          {t('Buy Now')}
                        </LinkButton>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mb-[30px] grid grid-cols-1 gap-[30px] lg:auto-rows-[455px] lg:grid-cols-3">
                {TT_DATA_2.map((item, idx) => (
                  <div
                    key={`ttd3-${idx}`}
                    className="group flex gap-4 rounded-[20px] border border-[#D9D9D9] bg-white p-6 text-[#002035]"
                  >
                    <div className="flex flex-col justify-center gap-[14px]">
                      <item.Icon className="w-[56px] transition-all duration-500 group-hover:w-[56px] lg:w-[100px]" />

                      <b
                        className={clsx(
                          'text-[24px] font-semibold text-[#004A61]',
                        )}
                      >
                        {t(item.title)}
                      </b>
                      <p className="text-[16px]">{t(item.desc)}</p>

                      <ul className="h-[120px] list-disc overflow-hidden pl-6 text-[16px] transition-all duration-500 group-hover:h-[120px] lg:h-0">
                        {item.desc2.map((desc, descIdx) => (
                          <li key={`ttd4-${descIdx}`}>{t(desc)}</li>
                        ))}
                      </ul>
                      <div className="flex">
                        <LinkButton
                          className="hidden group-hover:hidden lg:flex"
                          type="text"
                          size="M"
                          url={redirectUrl}
                          target="_blank"
                        >
                          {t('Learn More')}
                        </LinkButton>
                        <LinkButton
                          className="flex group-hover:flex lg:hidden"
                          type="main"
                          size="M"
                          url={redirectUrl}
                          target="_blank"
                          onClick={handleConversionClick}
                        >
                          {t('Buy Now')}
                        </LinkButton>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="group relative justify-center overflow-hidden rounded-[20px] bg-[#004A61] lg:flex">
                <div className="relative z-1 flex flex-col justify-center gap-[14px] px-6 py-10 text-white lg:p-6">
                  <div className="flex">
                    <TT6 className="w-[56px] transition-all duration-500 group-hover:w-[56px] lg:w-[100px]" />
                    <TT7 className="w-[56px] transition-all duration-500 group-hover:w-[56px] lg:w-[100px]" />
                  </div>

                  <b className="text-[24px] font-semibold">
                    {t('PRO Series Accelerated IP')}
                  </b>
                  <p className="text-[16px]">
                    {t(
                      'Ultra-high-quality IPs with low latency, optimised to match your unique business scenarios.',
                    )}
                  </p>
                  <ul className="h-[120px] list-disc overflow-hidden pl-6 text-[16px] transition-all duration-500 group-hover:h-[120px] lg:h-0">
                    <li>{t('Perfect IP matching')}</li>
                    <li>{t('Higher IP quality and authority')}</li>
                    <li>{t('Superior stability')}</li>
                    <li>{t('Premium data centre selection')}</li>
                  </ul>

                  <div className="flex">
                    <LinkButton
                      className="hidden group-hover:hidden lg:flex"
                      type="text"
                      size="M"
                      url={redirectUrl}
                      target="_blank"
                    >
                      {t('Learn More')}
                    </LinkButton>
                    <LinkButton
                      className="flex group-hover:flex lg:hidden"
                      type="main"
                      size="M"
                      url={redirectUrl}
                      target="_blank"
                      onClick={handleConversionClick}
                    >
                      {t('Buy Now')}
                    </LinkButton>
                  </div>
                </div>

                <div className="lg:relative">
                  <div className="absolute top-0 right-0 bottom-0 left-0 bg-linear-[89deg,#004A61_0.55%,transparent_94.8%]"></div>

                  <Image
                    src={ttBg}
                    alt=""
                    className="mix-blend-luminosity"
                    fill={isMobile}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TopTier;
