import { type Locale } from '@hi7/lib/i18n';

import { type Locale } from '@hi7/lib/i18n';

/**
 * Helper function to generate hreflang alternates for SEO
 * Since the app uses cookie-based localization, we use query parameters for hreflang
 */
export function generateHreflangAlternates(
  baseUrl: string,
  pathname: string = '',
) {
  const fullUrl = `${baseUrl}${pathname}`;

  return {
    en: `${fullUrl}?lang=en`,
    zh: `${fullUrl}?lang=zh`,
    'en-US': `${fullUrl}?lang=en`,
    'en-GB': `${fullUrl}?lang=en`,
    'en-AU': `${fullUrl}?lang=en`,
    'en-CA': `${fullUrl}?lang=en`,
    'zh-CN': `${fullUrl}?lang=zh`,
    'zh-TW': `${fullUrl}?lang=zh`,
    'zh-HK': `${fullUrl}?lang=zh`,
    'zh-SG': `${fullUrl}?lang=zh`,
    'x-default': fullUrl,
  };
}

/**
 * Generate canonical URL for the current page
 */
export function generateCanonicalUrl(baseUrl: string, pathname: string = '') {
  return `${baseUrl}${pathname}`;
}

/**
 * Get the proper locale code for OpenGraph and other meta tags
 */
export function getOpenGraphLocale(locale: Locale) {
  return locale === 'zh' ? 'zh_CN' : 'en_US';
}

/**
 * Get the alternate locale for OpenGraph
 */
export function getAlternateLocale(locale: Locale) {
  return locale === 'zh' ? 'en_US' : 'zh_CN';
}
