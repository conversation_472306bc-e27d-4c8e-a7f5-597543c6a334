'use client';

import EdgeMap from '@hi7/assets/icon/edge-map.svg';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { EDGE_DATA } from './config';

function CompactitiveEdge() {
  const [index, setIndex] = useState(0);
  const t = useI18n();

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prev) => (prev + 1) % EDGE_DATA.length);
    }, 8000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative">
          <div className="relative flex flex-col items-center justify-center px-5 py-11 text-[#002035] lg:pt-[72px]">
            <div className="text-center lg:w-[60dvw] lg:min-w-[855px]">
              <h2 className="mb-[16px] text-[36px] leading-none lg:text-[48px]">
                {t('Our Competitive ')}
                <span className="font-orbitron font-semibold text-[#31F4A0]">
                  {t('Edge')}
                </span>
              </h2>
              <h3 className="mb-[10px] text-[18px] leading-[25.2px] text-[#656578]">
                {t(
                  'We provide rigorously tested, authentic, and high-quality IPs maintained under stringent technical standards. Our professional research team ensures every exclusive IP delivers high anonymity and exceptional performance',
                )}
              </h3>
            </div>

            <div className="relative w-full lg:w-[65dvw] lg:min-w-[950px]">
              <EdgeMap />
              {EDGE_DATA.map(
                (item, idx) =>
                  index === idx && (
                    <>
                      <div
                        key={`edge-${index}`}
                        className="absolute top-0 left-0 flex h-full w-full items-center justify-center"
                      >
                        {item.background}
                      </div>
                      <div
                        key={`edge2-${index}`}
                        className="absolute top-0 left-0 flex h-full w-full items-center justify-center"
                      >
                        <div className="flex flex-col items-center text-center">
                          <h2 className="text-[64px] leading-[1.2] font-semibold text-[#007D93] lg:text-[96px]">
                            {item.title}
                          </h2>
                          <p className="text-[20px] leading-[1.4] whitespace-nowrap text-[#002035] lg:text-[32px]">
                            {t(item.description)}
                          </p>
                        </div>
                      </div>
                    </>
                  ),
              )}
            </div>

            <div className="relative m-auto grid w-[76px] grid-cols-4 gap-x-3">
              {EDGE_DATA.map((_, idx) => (
                <div
                  key={`edge3-${idx}`}
                  className={clsx([
                    'h-[10px] w-[10px] cursor-pointer rounded-full transition-all duration-300 ease-in-out',
                    index === idx ? 'bg-[#31F4A0]' : 'bg-[#D9D9D9]',
                  ])}
                  onMouseEnter={() => setIndex(idx)}
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CompactitiveEdge;
