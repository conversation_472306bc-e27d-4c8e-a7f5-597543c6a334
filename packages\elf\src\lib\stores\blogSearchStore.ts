// src/stores/searchStore.ts
import { create } from 'zustand';

interface SearchStore {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  resetSearchResult: () => void;
  result: boolean;
  setResult: (page: boolean) => void;
}

const useSearchStore = create<SearchStore>((set) => ({
  searchQuery: '',
  setSearchQuery: (query) => set({ searchQuery: query }),
  resetSearchResult: () => set({ searchQuery: '' }),
  result: false,
  setResult: (page: boolean) => set({ result: page }),
}));

export default useSearchStore;
