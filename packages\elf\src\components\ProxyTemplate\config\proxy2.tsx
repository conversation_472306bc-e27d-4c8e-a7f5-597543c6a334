import P1 from '@hi7/assets/icon/proxy-2-1.svg';
import P2 from '@hi7/assets/icon/proxy-2-2.svg';
import P3 from '@hi7/assets/icon/proxy-2-3.svg';
import P4 from '@hi7/assets/icon/proxy-2-4.svg';
import P5 from '@hi7/assets/icon/proxy-2-5.svg';
import P6 from '@hi7/assets/icon/proxy-2-6.svg';
// import MaskProxy1 from '@hi7/assets/icon/proxy-2-bg-2.svg';
import proxy from '@hi7/assets/lotties/proxy-2';

export const benefit = [
  {
    Icon: P1,
    title: 'Bypassing Restrictions',
    desc: 'Overcome platform limits on mass activities from a single IP and avoid geo-blocked content',
  },
  {
    Icon: P2,
    title: 'High Quality & Anonymity',
    desc: 'Overcome platform limits on mass activities from a single IP and avoid geo-blocked content',
  },

  {
    Icon: P3,
    title: 'Cost-Effective',
    desc: 'Enjoy unlimited bandwidth and automatic IP switching to maximise your ROI.',
  },
] as const;

export const useCase = [
  {
    Icon: P4,
    title: 'Data Collection',
    desc: 'Seamlessly scrape data without encountering IP bans.',
  },
  {
    Icon: P5,
    title: 'Security Testing',
    desc: 'Identify vulnerabilities by simulating diverse network conditions.',
  },
  {
    Icon: P6,
    title: 'Ad Verification',
    desc: 'Monitor ad placements and detect fraudulent activity in real time.',
  },
] as const;

export const PROXY = {
  benefit,
  useCase,
  theme: 'dark',
  title: {
    h3: 'High-Value',
    h1: 'Dynamic Data Centre Proxy',
    p: 'Harness the power of ElfProxy’s Dynamic Data Centre Proxy to effortlessly bypass geo-restrictions and access data from any region. Our data centre IPs are carefully curated to ensure high quality and anonymity, giving you the competitive edge in data collection and online operations.',
  },
  cta: 'Starting at $0.6/GB',
  background: {
    left: 'bg-[#004A61]',
    right: 'text-[#007D93]',
  },
  lottie: {
    // Masking: MaskProxy1, // TBC
    data: proxy,
  },
} as const;

export default PROXY;
