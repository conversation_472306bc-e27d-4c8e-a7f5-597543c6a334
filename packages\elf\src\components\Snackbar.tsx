import CorrectIcon from '@hi7/assets/icon/correct.svg';
import WrongIcon from '@hi7/assets/icon/wrong.svg';
import type { SnackbarType } from '@hi7/interface/snackbar';
import type React from 'react';

interface SnackbarProps {
  message: string;
  show: boolean;
  type: SnackbarType;
}

const Snackbar: React.FC<SnackbarProps> = ({ message, show, type }) => {
  if (!show) return null;

  return (
    <div className="fixed right-0 bottom-28 left-0 mx-auto w-75 rounded-lg bg-[#FAFAFA] px-4 py-2 shadow-lg">
      <div className="flex items-center gap-2">
        <div className={`w-4`}>
          {type === 'error' ? (
            <WrongIcon className={`w-4`} />
          ) : (
            <CorrectIcon className={`w-4`} />
          )}
        </div>

        <div className="break-all">{message}</div>
      </div>
    </div>
  );
};

export default Snackbar;
