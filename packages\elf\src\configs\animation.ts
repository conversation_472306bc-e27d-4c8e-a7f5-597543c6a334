import type { Target, Transition } from 'motion/react';

export type VariantType =
  | 'SlideIn'
  | 'SlideOut'
  | 'SlideUp'
  | 'FadeIn'
  | 'SlideDown'
  | 'SlideDownDelay';

type MotionConfig = {
  initial: Target;
  animate: Target;
  transition: Transition;
};

export const defaultAnimateConfig: Record<VariantType, MotionConfig> = {
  SlideIn: {
    initial: { x: 100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideOut: {
    initial: { x: -100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideUp: {
    initial: { y: 100, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideDown: {
    initial: { y: -450, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.2, delay: 0.1 },
  },
  SlideDownDelay: {
    initial: { y: -250, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.2, delay: 0.5 },
  },

  FadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
};
