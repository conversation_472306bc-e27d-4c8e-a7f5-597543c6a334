import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Best Residential Proxies - Hide Your IP, Access Globally, Manage Multiple Accounts Securely',
    ),
    description: t(locale)(
      'ElfProxy provides high-anonymity residential proxy services with genuine residential IPs from over 220 countries. Effectively hide your real IP address and encrypt data transmission. Bypass geo-restrictions to access platforms like Netflix, Google, and more. Support independent IP isolation for managing multiple web accounts, enhancing security. Start your free trial now!',
    ),
    keywords: t(locale)(
      'Web browsing proxy IP, high-anonymity IP, global network acceleration, multi-account anti-association, web account anti-association',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-3" />;
}

export default Solution;
