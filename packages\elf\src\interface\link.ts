import type { LinkProps } from '@hi7/components/Link';

type SubItemLink = {
  Icon?: React.ComponentType<{ width?: number; height?: number }>;
  url: string;
  text: string;
  subText?: string;
  order?: string;
};

export type MenuLinkProps = LinkProps & {
  open?: boolean;
  asButton?: boolean;

  icon?: React.ReactNode;
  items?: SubItemLink[];

  children: React.ReactNode | string;
  url?: string;
  order?: string;
};

export type MediaProps = {
  url: string;
  icon: React.ReactNode;
  target?: '_blank' | '_parent' | '_self' | '_top';
  children?: React.ReactNode;
};
