import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Static Data Center IP Proxy - Static Data Center IP - ElfProxy Pure Data Center IP',
    ),
    description: t(locale)(
      'Global high-anonymity data center IP free trial, providing high stability and high-speed bandwidth (starting from ≥1Gbps) network services. 100% dedicated IP, can filter out high-purity IPs according to your business needs. Provides static data center IP, fixed IP, long-term IP, supports HTTP(s) proxy and Socks5 proxy protocols.',
    ),
    keywords: t(locale)(
      'Static IP, Data Center IP, Static Data Center Proxy, Data Center Proxy, Fixed IP, Data Center IP Trial, Data Center IP Purchase',
    ),
  };
};

function Proxy2() {
  return <ProxyTemplate id="proxy-5" />;
}

export default Proxy2;
