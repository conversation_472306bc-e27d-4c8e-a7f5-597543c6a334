'use client';

export default function FloatContact() {
  return (
    <>
      <style>{`
        .gfrzfpcx_scrm {
          position: fixed;
          bottom: 21vh;
          right: 21px;
          text-align: center;
          cursor: pointer;
          z-index: 40;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .main-button {
          width: 135px;
          height: 135px;
          margin-bottom: 15px;
          display: flex;
          justify-content: center;
        }
        .main-button img {
          width: 135px;
          height: 135px;
          object-fit: contain;
          transition: transform 0.3s ease;
        }
        .main-button img:hover {
          transform: scale(1.05);
        }

        .button-containers {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 10px;
          margin-top: 10px;
          width: 140px;
        }
        .button-containers a {
          display: flex;
          justify-content: center;
          width: 140px;
          height: 26.33px;
        }
        .button-containers img {
          width: 140px;
          height: 26.33px;
          object-fit: contain;
          transition: transform 0.3s ease;
        }
        .button-containers img:hover {
          transform: scale(1.05);
        }

        @media screen and (max-width: 768px) {
          .gfrzfpcx_scrm {
            width: auto !important;
          }
          .main-button {
            width: 120px !important;
            height: 120px !important;
            margin-bottom: 10px
          }
          .main-button img {
            width: 120px !important;
            height: 120px !important;
          }
          .button-containers {
            width: 120px !important;
          }
          .button-containers a {
            width: 120px !important;
            height: auto !important;
          }
          .button-containers img {
            width: 120px !important;
            height: auto !important;
          }
        }
      `}</style>

      <div className="gfrzfpcx_scrm">
        <div className="main-button">
          <a
            href="https://007tg.com/ccs/elfproxy"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="https://ctrlfire.com/wp-content/uploads/2025/04/联系客服.png"
              alt="联系Elfproxy客服"
            />
          </a>
        </div>

        <div className="button-containers">
          <a
            href="https://007tg.com/ccs/elfproxy"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="https://ctrlfire.com/wp-content/uploads/2025/04/telegram-.png"
              alt="Button TG"
            />
          </a>
          <a
            href="https://t.me/elfproxy"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="https://ctrlfire.com/wp-content/uploads/2025/08/TG官方频道.png"
              alt="Button TG官方频道"
            />
          </a>
          <a
            href="https://t.me/ElfProxyChat"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="https://ctrlfire.com/wp-content/uploads/2025/08/TG交流群.png"
              alt="Button TG交流群"
            />
          </a>
          <a
            href="https://007tg.com/ccs/elfproxy_wa"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="https://ctrlfire.com/wp-content/uploads/2025/04/whats-APP-.png"
              alt="Button WA"
            />
          </a>
          <a
            href="https://whatsapp.com/channel/0029VbB7EfR7dmeeXdv67G07"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="https://ctrlfire.com/wp-content/uploads/2025/08/WhatsApp官方频道.png"
              alt="Button WA官方频道"
            />
          </a>
          <a
            href="https://chat.whatsapp.com/LiwHQRdZD8uHlBNYzx3JUV"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="https://ctrlfire.com/wp-content/uploads/2025/08/WhatsApp交流群.png"
              alt="Button WA交流群"
            />
          </a>
        </div>
      </div>
    </>
  );
}
