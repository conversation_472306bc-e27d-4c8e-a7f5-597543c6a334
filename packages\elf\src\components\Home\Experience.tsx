'use client';

import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import LinkButton from '../Form/LinkButton';

function Experience() {
  const locale = useLocale();
  const t = useI18n();
  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative flex h-[850px] flex-col lg:block lg:h-auto">
          <video
            playsInline
            autoPlay
            muted
            className="relative top-[35%] right-[-50%] left-[-50%] -z-10 order-2 w-[200%] max-w-none lg:top-0 lg:right-0 lg:left-0 lg:order-1 lg:w-dvw"
          >
            <source
              src={`${process.env.NEXT_PUBLIC_ASSET_PREFIX || ''}/videos/home-2.mp4`}
              type="video/mp4"
            />
          </video>
          <div className="absolute top-0 right-0 left-0 order-1 flex flex-col items-center justify-center bg-[#002035] px-5 py-11 lg:order-2 lg:bg-transparent lg:pt-[72px]">
            <div className="lg:w-[85dvw] lg:min-w-[1200px]">
              <div className="flex flex-col items-center justify-center gap-[30px] text-white">
                <div className="text-center text-[24px] leading-[1.2] lg:text-[48px]">
                  {t('Experience the ')}
                  <span className="block">
                    <span className="font-orbitron text-[36px] font-semibold text-[#31F4A0] lg:text-[48px]">
                      {t('World’s Top IP Proxy')}
                    </span>
                    <span className="text-[36px] lg:text-[48px]">
                      {t(' Service Today')}
                    </span>
                  </span>
                </div>

                <h3 className="text-center leading-[25.2px] lg:text-[18px]">
                  <span
                    className={clsx(
                      'font-orbitron text-[#31F4A0]',
                      locale === 'zh' && 'order-1',
                    )}
                  >
                    {t('FREE')}
                  </span>
                  <span className={clsx(locale === 'zh' && 'order-2')}>
                    {t(
                      'Get 10 Static Residential IPv6 IPs + 200MB of Dynamic Residential Traffic for ',
                    )}
                  </span>
                </h3>
                <LinkButton
                  type="main"
                  size="L"
                  url={
                    locale === 'en'
                      ? 'https://007tg.com/ccs/elfproxy'
                      : 'https://007tg.com/ccs/elfproxy'
                  }
                  target="_blank"
                >
                  {t('Try Now')}
                </LinkButton>
                <p className="text-center text-[12px]">
                  {t(
                    'Note: Due to policy restrictions, our service is currently unavailable in Mainland China, Hong Kong, Macau, Taiwan and Russia.',
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Experience;
