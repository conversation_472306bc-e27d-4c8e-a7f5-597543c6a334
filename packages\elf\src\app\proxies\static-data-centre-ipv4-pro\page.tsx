import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { getLocale } from '@hi7/helpers/pathname';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async () => {
  const locale = await getLocale();
  return {
    title: t(locale)(
      'Static Data Center IP Proxy - Relay IP/Accelerated IP - Global Network Latency 20-80ms',
    ),
    description: t(locale)(
      'Global high-stability data center IP free trial, with relay acceleration achieving 20-80ms latency, just like using an international dedicated line, each IP is dedicated for exclusive use. Suitable for high-stability, low-latency businesses: enterprise operations, international data transmission, and more. 99.9% online stability, fixed IP and long-term IP for long-term use.',
    ),
    keywords: t(locale)(
      'Dedicated Line Relay, Node Relay, Dedicated Line Acceleration, Relay IP, Accelerated IP, Data Center IP Relay, IP Latency',
    ),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.elfproxy.com'}/proxies/static-data-centre-ipv4-pro`,
    },
  };
};

function Proxy() {
  return <ProxyTemplate id="proxy-6" />;
}

export default Proxy;
